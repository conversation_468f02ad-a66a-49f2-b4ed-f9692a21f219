#!/usr/bin/env python3
"""
🏆 WINNING SOLUTION 🏆
Based on your best 1120 score solution with targeted improvements
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestRegressor, ExtraTreesRegressor
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.metrics import mean_squared_error
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
import lightgbm as lgb
import xgboost as xgb

class WinningSolution:
    def __init__(self):
        self.models = {}
        self.feature_columns = []
        self.route_stats = {}
        self.route_clusters = {}
        
    def load_data(self):
        """Load training and test datasets"""
        print("Loading data...")
        
        self.train_df = pd.read_csv('train_JDXlpm8/train/train.csv')
        self.test_df = pd.read_csv('test_8gqdJqH.csv')
        self.sample_submission = pd.read_csv('sample_submission_TQv3O0x.csv')
        
        print(f"Training data: {self.train_df.shape}")
        print(f"Test data: {self.test_df.shape}")
        
        return self
    
    def create_holiday_calendar(self):
        """Create holiday calendar - EXACT same as your best solution"""
        holidays = [
            '2023-01-26', '2023-03-08', '2023-04-14', '2023-08-15', '2023-10-02', '2023-11-13', '2023-12-25',
            '2024-01-26', '2024-03-25', '2024-04-17', '2024-08-15', '2024-10-02', '2024-11-01', '2024-12-25',
            '2025-01-26', '2025-03-14', '2025-04-06', '2025-08-15', '2025-10-02', '2025-10-20', '2025-12-25'
        ]
        self.holidays = pd.to_datetime(holidays)
        return self
    
    def engineer_enhanced_features(self, df, is_train=True):
        """Create enhanced feature set - BASED on your best solution with small improvements"""
        print(f"Engineering features for {'training' if is_train else 'test'} data...")
        
        df = df.copy()
        df['doj'] = pd.to_datetime(df['doj'])
        
        # Basic temporal features - EXACT same
        df['year'] = df['doj'].dt.year
        df['month'] = df['doj'].dt.month
        df['day'] = df['doj'].dt.day
        df['dayofweek'] = df['doj'].dt.dayofweek
        df['dayofyear'] = df['doj'].dt.dayofyear
        df['week'] = df['doj'].dt.isocalendar().week
        df['quarter'] = df['doj'].dt.quarter
        
        # Advanced temporal features - EXACT same
        df['is_weekend'] = (df['dayofweek'] >= 5).astype(int)
        df['is_month_start'] = (df['day'] <= 5).astype(int)
        df['is_month_end'] = (df['day'] >= 25).astype(int)
        df['is_quarter_start'] = ((df['day'] <= 5) & (df['month'].isin([1, 4, 7, 10]))).astype(int)
        df['is_quarter_end'] = ((df['day'] >= 25) & (df['month'].isin([3, 6, 9, 12]))).astype(int)
        
        # Cyclical features - EXACT same
        df['day_sin'] = np.sin(2 * np.pi * df['dayofyear'] / 365.25)
        df['day_cos'] = np.cos(2 * np.pi * df['dayofyear'] / 365.25)
        df['week_sin'] = np.sin(2 * np.pi * df['dayofweek'] / 7)
        df['week_cos'] = np.cos(2 * np.pi * df['dayofweek'] / 7)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        
        # Holiday features - EXACT same
        df['days_to_holiday'] = df['doj'].apply(self._days_to_holiday)
        df['is_holiday'] = df['doj'].isin(self.holidays).astype(int)
        df['is_pre_holiday'] = df['doj'].apply(lambda x: (x + timedelta(days=1)) in self.holidays.values).astype(int)
        df['is_post_holiday'] = df['doj'].apply(lambda x: (x - timedelta(days=1)) in self.holidays.values).astype(int)
        df['holiday_week'] = df['doj'].apply(lambda x: any(abs((x - h).days) <= 7 for h in self.holidays)).astype(int)
        
        # Route features - EXACT same
        df['route'] = df['srcid'].astype(str) + '_' + df['destid'].astype(str)
        df['route_distance'] = np.sqrt((df['srcid'] - df['destid'])**2)
        
        # Seasonal features - EXACT same
        df['season'] = df['month'].map({12: 0, 1: 0, 2: 0, 3: 1, 4: 1, 5: 1, 6: 2, 7: 2, 8: 2, 9: 3, 10: 3, 11: 3})
        
        # Business vs leisure indicators - EXACT same
        df['is_business_day'] = ((df['dayofweek'] < 5) & (~df['is_holiday'])).astype(int)
        df['is_leisure_time'] = (df['is_weekend'] | df['is_holiday']).astype(int)
        
        # Special periods - EXACT same
        df['is_long_weekend'] = ((df['dayofweek'] == 4) & df['is_pre_holiday']) | ((df['dayofweek'] == 0) & df['is_post_holiday'])
        df['is_long_weekend'] = df['is_long_weekend'].astype(int)
        
        # Economic indicators - EXACT same
        df['is_month_end_salary'] = (df['day'] >= 28).astype(int)
        df['is_month_start_spending'] = (df['day'] <= 5).astype(int)
        
        # SMALL IMPROVEMENTS - only add a few proven features
        df['is_friday'] = (df['dayofweek'] == 4).astype(int)
        df['is_monday'] = (df['dayofweek'] == 0).astype(int)
        df['route_sum'] = df['srcid'] + df['destid']
        df['route_diff'] = np.abs(df['srcid'] - df['destid'])
        
        if is_train:
            self._calculate_route_stats(df)
            self._create_route_clusters(df)
        
        df = self._add_route_features(df)
        
        return df
    
    def _days_to_holiday(self, date):
        """Calculate days to nearest holiday - EXACT same"""
        if len(self.holidays) == 0:
            return 365
        days_diff = np.abs((self.holidays - date).days)
        return days_diff.min()
    
    def _calculate_route_stats(self, df):
        """Calculate route statistics - EXACT same"""
        print("Calculating route statistics...")
        
        # Basic route stats
        route_stats = df.groupby('route')['final_seatcount'].agg([
            'mean', 'std', 'median', 'min', 'max', 'count'
        ]).reset_index()
        route_stats.columns = ['route', 'route_mean', 'route_std', 'route_median', 'route_min', 'route_max', 'route_count']
        
        # Day of week patterns
        dow_stats = df.groupby(['route', 'dayofweek'])['final_seatcount'].mean().reset_index()
        dow_pivot = dow_stats.pivot(index='route', columns='dayofweek', values='final_seatcount')
        dow_pivot.columns = [f'route_dow_{int(col)}_mean' for col in dow_pivot.columns]
        dow_pivot = dow_pivot.reset_index()
        
        # Month patterns
        month_stats = df.groupby(['route', 'month'])['final_seatcount'].mean().reset_index()
        month_pivot = month_stats.pivot(index='route', columns='month', values='final_seatcount')
        month_pivot.columns = [f'route_month_{int(col)}_mean' for col in month_pivot.columns]
        month_pivot = month_pivot.reset_index()
        
        # Holiday patterns
        holiday_stats = df.groupby(['route', 'is_holiday'])['final_seatcount'].mean().reset_index()
        holiday_pivot = holiday_stats.pivot(index='route', columns='is_holiday', values='final_seatcount')
        holiday_pivot.columns = [f'route_holiday_{int(col)}_mean' for col in holiday_pivot.columns]
        holiday_pivot = holiday_pivot.reset_index()
        
        # Merge all
        self.route_stats = route_stats.merge(dow_pivot, on='route', how='left')
        self.route_stats = self.route_stats.merge(month_pivot, on='route', how='left')
        self.route_stats = self.route_stats.merge(holiday_pivot, on='route', how='left')
        
        # Fill missing values
        for col in self.route_stats.columns:
            if col != 'route':
                self.route_stats[col] = self.route_stats[col].fillna(self.route_stats[col].mean())
    
    def _create_route_clusters(self, df):
        """Create route clusters - EXACT same"""
        print("Creating route clusters...")
        
        route_features = df.groupby('route')['final_seatcount'].agg(['mean', 'std', 'count']).fillna(0)
        
        if len(route_features) > 10:
            n_clusters = min(10, len(route_features) // 5)
            scaler = StandardScaler()
            scaled_features = scaler.fit_transform(route_features)
            
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            clusters = kmeans.fit_predict(scaled_features)
            
            self.route_clusters = dict(zip(route_features.index, clusters))
        else:
            self.route_clusters = {route: 0 for route in route_features.index}
    
    def _add_route_features(self, df):
        """Add route features - EXACT same"""
        df = df.merge(self.route_stats, on='route', how='left')
        df['route_cluster'] = df['route'].map(self.route_clusters).fillna(0)
        
        # Fill missing values
        for col in self.route_stats.columns:
            if col != 'route' and col in df.columns:
                df[col] = df[col].fillna(df[col].mean())
        
        return df
    
    def prepare_features(self):
        """Prepare feature matrices - EXACT same"""
        print("Preparing features...")
        
        self.train_features = self.engineer_enhanced_features(self.train_df, is_train=True)
        self.test_features = self.engineer_enhanced_features(self.test_df, is_train=False)
        
        exclude_cols = ['doj', 'final_seatcount', 'route_key', 'route']
        self.feature_columns = [col for col in self.train_features.columns if col not in exclude_cols]
        
        print(f"Number of features: {len(self.feature_columns)}")
        
        self.X_train = self.train_features[self.feature_columns].fillna(0)
        self.y_train = self.train_features['final_seatcount']
        self.X_test = self.test_features[self.feature_columns].fillna(0)
        
        return self
    
    def train_models(self):
        """Train optimized models - IMPROVED hyperparameters"""
        print("Training models...")
        
        # Random Forest - IMPROVED (was best in your solution)
        print("Training Random Forest...")
        rf = RandomForestRegressor(
            n_estimators=200,  # Increased from 150
            max_depth=25,      # Increased from 20
            min_samples_split=3,  # Decreased from default
            min_samples_leaf=1,   # Decreased from default
            random_state=42, 
            n_jobs=-1
        )
        rf.fit(self.X_train, self.y_train)
        self.models['rf'] = rf
        
        # Extra Trees - ADDED for diversity
        print("Training Extra Trees...")
        et = ExtraTreesRegressor(
            n_estimators=200,
            max_depth=25,
            min_samples_split=3,
            min_samples_leaf=1,
            random_state=42,
            n_jobs=-1
        )
        et.fit(self.X_train, self.y_train)
        self.models['et'] = et
        
        # LightGBM - IMPROVED
        print("Training LightGBM...")
        lgb_model = lgb.LGBMRegressor(
            n_estimators=300,  # Increased from 200
            max_depth=15,      # Increased from 12
            learning_rate=0.03, # Decreased from 0.05
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42, 
            verbose=-1
        )
        lgb_model.fit(self.X_train, self.y_train)
        self.models['lgb'] = lgb_model
        
        # XGBoost - IMPROVED
        print("Training XGBoost...")
        xgb_model = xgb.XGBRegressor(
            n_estimators=300,  # Increased from 200
            max_depth=12,      # Increased from 10
            learning_rate=0.03, # Decreased from 0.05
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42, 
            verbosity=0
        )
        xgb_model.fit(self.X_train, self.y_train)
        self.models['xgb'] = xgb_model
        
        return self
    
    def validate_models(self):
        """Validate models - EXACT same"""
        print("Validating models...")
        
        tscv = TimeSeriesSplit(n_splits=3)
        
        for name, model in self.models.items():
            scores = cross_val_score(model, self.X_train, self.y_train, cv=tscv, scoring='neg_root_mean_squared_error')
            print(f"{name.upper()} - RMSE: {-scores.mean():.2f} (+/- {scores.std() * 2:.2f})")
        
        return self
    
    def make_predictions(self):
        """Make ensemble predictions - IMPROVED weights"""
        print("Making predictions...")
        
        predictions = {}
        for name, model in self.models.items():
            pred = model.predict(self.X_test)
            predictions[name] = pred
            print(f"{name.upper()} - Mean: {pred.mean():.2f}, Std: {pred.std():.2f}")
        
        # IMPROVED ensemble weights (RF was best, so increase its weight)
        weights = {'rf': 0.4, 'et': 0.2, 'lgb': 0.25, 'xgb': 0.15}
        final_pred = np.zeros(len(self.X_test))
        
        for name, weight in weights.items():
            final_pred += weight * predictions[name]
        
        final_pred = np.maximum(final_pred, 0)
        print(f"ENSEMBLE - Mean: {final_pred.mean():.2f}, Std: {final_pred.std():.2f}")
        
        return final_pred
    
    def create_submission(self, predictions):
        """Create submission - EXACT same"""
        print("Creating submission...")
        
        submission = self.sample_submission.copy()
        submission['final_seatcount'] = predictions.round(1)
        submission.to_csv('winning_submission.csv', index=False)
        
        print("Submission saved: winning_submission.csv")
        return submission
    
    def run_pipeline(self):
        """Run complete pipeline"""
        print("="*60)
        print("🏆 WINNING SOLUTION - BEAT 1120 SCORE 🏆")
        print("="*60)
        
        self.load_data()
        self.create_holiday_calendar()
        self.prepare_features()
        self.train_models()
        self.validate_models()
        
        predictions = self.make_predictions()
        submission = self.create_submission(predictions)
        
        print("="*60)
        print("🚀 WINNING PIPELINE COMPLETED! 🚀")
        print("="*60)
        
        return submission

def main():
    predictor = WinningSolution()
    submission = predictor.run_pipeline()
    
    print(f"\n🏆 WINNING SOLUTION SUMMARY:")
    print(f"Mean: {submission['final_seatcount'].mean():.2f}")
    print(f"Median: {submission['final_seatcount'].median():.2f}")
    print(f"Std: {submission['final_seatcount'].std():.2f}")
    print(f"Range: {submission['final_seatcount'].min():.2f} - {submission['final_seatcount'].max():.2f}")

if __name__ == "__main__":
    main()
