#!/usr/bin/env python3
"""
🎯 SUB-600 SOLUTION 🎯
Bus Demand Prediction - TARGET: Sub-600 Score

🔥 ULTRA-OPTIMIZED FOR MAXIMUM PERFORMANCE
- Deep route pattern analysis
- Advanced temporal modeling
- Sophisticated ensemble with 6 models
- Target validation: Sub-700 RMSE
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestRegressor, ExtraTreesRegressor, GradientBoostingRegressor
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.metrics import mean_squared_error
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.cluster import KMeans
from sklearn.linear_model import Ridge, ElasticNet
import lightgbm as lgb
import xgboost as xgb

class Sub600Solution:
    def __init__(self):
        self.models = {}
        self.feature_columns = []
        self.route_stats = {}
        self.route_clusters = {}
        self.major_routes = set()
        
    def load_data(self):
        """Load and analyze data"""
        print("Loading data...")
        
        self.train_df = pd.read_csv('train_JDXlpm8/train/train.csv')
        self.test_df = pd.read_csv('test_8gqdJqH.csv')
        self.sample_submission = pd.read_csv('sample_submission_TQv3O0x.csv')
        
        print(f"Training data: {self.train_df.shape}")
        print(f"Test data: {self.test_df.shape}")
        
        # Identify major routes (top 10% by volume)
        self.train_df['route'] = self.train_df['srcid'].astype(str) + '_' + self.train_df['destid'].astype(str)
        route_volumes = self.train_df.groupby('route')['final_seatcount'].mean()
        threshold = route_volumes.quantile(0.9)
        self.major_routes = set(route_volumes[route_volumes >= threshold].index)
        print(f"Identified {len(self.major_routes)} major routes")
        
        return self
    
    def create_holiday_calendar(self):
        """Enhanced holiday calendar"""
        holidays = [
            '2023-01-26', '2023-03-08', '2023-04-14', '2023-08-15', '2023-10-02', '2023-11-13', '2023-12-25',
            '2024-01-26', '2024-03-25', '2024-04-17', '2024-08-15', '2024-10-02', '2024-11-01', '2024-12-25',
            '2025-01-26', '2025-03-14', '2025-04-06', '2025-08-15', '2025-10-02', '2025-10-20', '2025-12-25'
        ]
        self.holidays = pd.to_datetime(holidays)
        
        # Holiday importance weights
        self.holiday_weights = {
            '2023-11-13': 1.0, '2024-11-01': 1.0, '2025-10-20': 1.0,  # Diwali - highest impact
            '2023-01-26': 0.9, '2024-01-26': 0.9, '2025-01-26': 0.9,  # Republic Day
            '2023-08-15': 0.9, '2024-08-15': 0.9, '2025-08-15': 0.9,  # Independence Day
            '2023-03-08': 0.8, '2024-03-25': 0.8, '2025-03-14': 0.8,  # Holi
        }
        
        return self
    
    def engineer_ultra_features(self, df, is_train=True):
        """Ultra-optimized feature engineering"""
        print(f"Engineering ultra features for {'training' if is_train else 'test'} data...")
        
        df = df.copy()
        df['doj'] = pd.to_datetime(df['doj'])
        
        # === CORE TEMPORAL FEATURES ===
        df['year'] = df['doj'].dt.year
        df['month'] = df['doj'].dt.month
        df['day'] = df['doj'].dt.day
        df['dayofweek'] = df['doj'].dt.dayofweek
        df['dayofyear'] = df['doj'].dt.dayofyear
        df['week'] = df['doj'].dt.isocalendar().week
        df['quarter'] = df['doj'].dt.quarter
        
        # === ADVANCED TEMPORAL FEATURES ===
        df['is_weekend'] = (df['dayofweek'] >= 5).astype(int)
        df['is_friday'] = (df['dayofweek'] == 4).astype(int)
        df['is_monday'] = (df['dayofweek'] == 0).astype(int)
        df['is_thursday'] = (df['dayofweek'] == 3).astype(int)
        df['is_month_start'] = (df['day'] <= 5).astype(int)
        df['is_month_end'] = (df['day'] >= 25).astype(int)
        df['is_quarter_start'] = ((df['day'] <= 5) & (df['month'].isin([1, 4, 7, 10]))).astype(int)
        df['is_quarter_end'] = ((df['day'] >= 25) & (df['month'].isin([3, 6, 9, 12]))).astype(int)
        
        # === ULTRA CYCLICAL FEATURES ===
        # Multiple periods with different harmonics
        periods = [7, 14, 30.44, 91.31, 365.25]
        for period in periods:
            df[f'sin_{period:.0f}'] = np.sin(2 * np.pi * df['dayofyear'] / period)
            df[f'cos_{period:.0f}'] = np.cos(2 * np.pi * df['dayofyear'] / period)
        
        # Day of week with harmonics
        df['dow_sin'] = np.sin(2 * np.pi * df['dayofweek'] / 7)
        df['dow_cos'] = np.cos(2 * np.pi * df['dayofweek'] / 7)
        df['dow_sin2'] = np.sin(4 * np.pi * df['dayofweek'] / 7)
        df['dow_cos2'] = np.cos(4 * np.pi * df['dayofweek'] / 7)
        
        # Month with harmonics
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        df['month_sin2'] = np.sin(4 * np.pi * df['month'] / 12)
        df['month_cos2'] = np.cos(4 * np.pi * df['month'] / 12)
        
        # === ULTRA HOLIDAY FEATURES ===
        df['days_to_holiday'] = df['doj'].apply(self._days_to_holiday)
        df['days_from_holiday'] = df['doj'].apply(self._days_from_holiday)
        df['holiday_weight'] = df['doj'].apply(self._get_holiday_weight)
        df['is_holiday'] = df['doj'].isin(self.holidays).astype(int)
        df['is_pre_holiday'] = df['doj'].apply(lambda x: (x + timedelta(days=1)) in self.holidays.values).astype(int)
        df['is_post_holiday'] = df['doj'].apply(lambda x: (x - timedelta(days=1)) in self.holidays.values).astype(int)
        df['holiday_week'] = df['doj'].apply(lambda x: any(abs((x - h).days) <= 7 for h in self.holidays)).astype(int)
        
        # Multiple holiday proximity ranges
        for days in [1, 2, 3, 5, 7, 10, 14]:
            df[f'holiday_within_{days}d'] = (df['days_to_holiday'] <= days).astype(int)
        
        # === ROUTE FEATURES ===
        df['route'] = df['srcid'].astype(str) + '_' + df['destid'].astype(str)
        df['reverse_route'] = df['destid'].astype(str) + '_' + df['srcid'].astype(str)
        df['route_distance'] = np.sqrt((df['srcid'] - df['destid'])**2)
        df['route_sum'] = df['srcid'] + df['destid']
        df['route_diff'] = np.abs(df['srcid'] - df['destid'])
        df['route_product'] = df['srcid'] * df['destid']
        df['route_ratio'] = df['srcid'] / (df['destid'] + 1)
        df['is_major_route'] = df['route'].isin(self.major_routes).astype(int)
        
        # Route direction features
        df['route_direction'] = np.where(df['srcid'] > df['destid'], 1, 0)
        df['route_magnitude'] = np.sqrt(df['srcid']**2 + df['destid']**2)
        
        # === SEASONAL & BUSINESS FEATURES ===
        df['season'] = df['month'].map({12: 0, 1: 0, 2: 0, 3: 1, 4: 1, 5: 1, 6: 2, 7: 2, 8: 2, 9: 3, 10: 3, 11: 3})
        df['is_summer'] = (df['season'] == 2).astype(int)
        df['is_winter'] = (df['season'] == 0).astype(int)
        df['is_spring'] = (df['season'] == 1).astype(int)
        df['is_autumn'] = (df['season'] == 3).astype(int)
        df['is_monsoon'] = df['month'].isin([6, 7, 8, 9]).astype(int)
        df['is_peak_travel'] = df['month'].isin([4, 5, 10, 11, 12]).astype(int)
        
        # Business logic
        df['is_business_day'] = ((df['dayofweek'] < 5) & (~df['is_holiday'])).astype(int)
        df['is_leisure_time'] = (df['is_weekend'] | df['is_holiday']).astype(int)
        df['is_long_weekend'] = ((df['dayofweek'] == 4) & df['is_pre_holiday']) | ((df['dayofweek'] == 0) & df['is_post_holiday'])
        df['is_long_weekend'] = df['is_long_weekend'].astype(int)
        
        # Economic indicators
        df['is_month_end_salary'] = (df['day'] >= 28).astype(int)
        df['is_month_start_spending'] = (df['day'] <= 5).astype(int)
        df['is_mid_month'] = ((df['day'] >= 10) & (df['day'] <= 20)).astype(int)
        
        # Advanced date features
        df['days_in_month'] = df['doj'].dt.days_in_month
        df['day_of_month_ratio'] = df['day'] / df['days_in_month']
        df['week_of_month'] = (df['day'] - 1) // 7 + 1
        
        if is_train:
            self._calculate_ultra_route_stats(df)
            self._create_ultra_clusters(df)
        
        df = self._add_ultra_route_features(df)
        
        return df
    
    def _days_to_holiday(self, date):
        """Days to nearest holiday"""
        if len(self.holidays) == 0:
            return 365
        return np.abs((self.holidays - date).days).min()
    
    def _days_from_holiday(self, date):
        """Days from nearest past holiday"""
        past_holidays = self.holidays[self.holidays <= date]
        if len(past_holidays) == 0:
            return 365
        return (date - past_holidays.max()).days
    
    def _get_holiday_weight(self, date):
        """Get holiday importance weight"""
        date_str = date.strftime('%Y-%m-%d')
        return self.holiday_weights.get(date_str, 0.5)
    
    def _calculate_ultra_route_stats(self, df):
        """Calculate ultra-detailed route statistics"""
        print("Calculating ultra route statistics...")
        
        # Comprehensive route stats
        route_stats = df.groupby('route')['final_seatcount'].agg([
            'mean', 'std', 'median', 'min', 'max', 'count', 'skew',
            lambda x: x.quantile(0.1), lambda x: x.quantile(0.25),
            lambda x: x.quantile(0.75), lambda x: x.quantile(0.9)
        ]).reset_index()
        
        route_stats.columns = ['route', 'route_mean', 'route_std', 'route_median', 
                              'route_min', 'route_max', 'route_count', 'route_skew',
                              'route_p10', 'route_p25', 'route_p75', 'route_p90']
        
        # Advanced derived metrics
        route_stats['route_iqr'] = route_stats['route_p75'] - route_stats['route_p25']
        route_stats['route_range'] = route_stats['route_p90'] - route_stats['route_p10']
        route_stats['route_cv'] = route_stats['route_std'] / (route_stats['route_mean'] + 1)
        
        # Multi-dimensional patterns
        # Day of week patterns
        dow_stats = df.groupby(['route', 'dayofweek'])['final_seatcount'].agg(['mean', 'std']).reset_index()
        dow_pivot = dow_stats.pivot(index='route', columns='dayofweek', values=['mean', 'std'])
        dow_pivot.columns = [f'route_dow_{int(col[1])}_{col[0]}' for col in dow_pivot.columns]
        dow_pivot = dow_pivot.reset_index()
        
        # Month patterns
        month_stats = df.groupby(['route', 'month'])['final_seatcount'].mean().reset_index()
        month_pivot = month_stats.pivot(index='route', columns='month', values='final_seatcount')
        month_pivot.columns = [f'route_month_{int(col)}_mean' for col in month_pivot.columns]
        month_pivot = month_pivot.reset_index()
        
        # Holiday patterns
        holiday_stats = df.groupby(['route', 'is_holiday'])['final_seatcount'].mean().reset_index()
        holiday_pivot = holiday_stats.pivot(index='route', columns='is_holiday', values='final_seatcount')
        holiday_pivot.columns = [f'route_holiday_{int(col)}_mean' for col in holiday_pivot.columns]
        holiday_pivot = holiday_pivot.reset_index()
        
        # Weekend patterns
        weekend_stats = df.groupby(['route', 'is_weekend'])['final_seatcount'].mean().reset_index()
        weekend_pivot = weekend_stats.pivot(index='route', columns='is_weekend', values='final_seatcount')
        weekend_pivot.columns = [f'route_weekend_{int(col)}_mean' for col in weekend_pivot.columns]
        weekend_pivot = weekend_pivot.reset_index()
        
        # Merge all
        self.route_stats = route_stats
        for stats_df in [dow_pivot, month_pivot, holiday_pivot, weekend_pivot]:
            self.route_stats = self.route_stats.merge(stats_df, on='route', how='left')
        
        # Fill missing values
        for col in self.route_stats.columns:
            if col != 'route':
                self.route_stats[col] = self.route_stats[col].fillna(self.route_stats[col].median())
    
    def _create_ultra_clusters(self, df):
        """Create ultra-sophisticated clusters"""
        print("Creating ultra clusters...")
        
        route_features = df.groupby('route')['final_seatcount'].agg([
            'mean', 'std', 'median', 'count', 'skew'
        ]).fillna(0)
        
        # Add temporal patterns
        dow_patterns = df.groupby(['route', 'dayofweek'])['final_seatcount'].mean().unstack(fill_value=0)
        dow_patterns.columns = [f'dow_{col}' for col in dow_patterns.columns]
        
        month_patterns = df.groupby(['route', 'month'])['final_seatcount'].mean().unstack(fill_value=0)
        month_patterns.columns = [f'month_{col}' for col in month_patterns.columns]
        
        # Combine features
        cluster_features = pd.concat([route_features, dow_patterns, month_patterns], axis=1).fillna(0)
        
        if len(cluster_features) > 15:
            scaler = StandardScaler()
            scaled_features = scaler.fit_transform(cluster_features)
            
            # Multiple clustering approaches
            n_clusters = min(20, max(5, len(cluster_features) // 4))
            
            # K-means
            kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
            kmeans_clusters = kmeans.fit_predict(scaled_features)
            
            self.route_clusters = {
                'kmeans': dict(zip(cluster_features.index, kmeans_clusters))
            }
        else:
            self.route_clusters = {
                'kmeans': {route: 0 for route in cluster_features.index}
            }
    
    def _add_ultra_route_features(self, df):
        """Add ultra route features"""
        # Add comprehensive route statistics
        df = df.merge(self.route_stats, on='route', how='left')
        
        # Add cluster information
        df['route_cluster'] = df['route'].map(self.route_clusters.get('kmeans', {})).fillna(0)
        
        # Route performance metrics
        if 'route_count' in df.columns:
            df['route_popularity'] = pd.cut(df['route_count'], bins=5, labels=[0,1,2,3,4], duplicates='drop').astype(float)
        
        if 'route_std' in df.columns and 'route_mean' in df.columns:
            df['route_volatility'] = df['route_std'] / (df['route_mean'] + 1)
            df['route_stability'] = 1 / (1 + df['route_volatility'])
        
        # Fill missing values
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            if col not in ['srcid', 'destid', 'year', 'month', 'day']:
                df[col] = df[col].fillna(df[col].median())
        
        return df

    def prepare_ultra_features(self):
        """Prepare ultra-optimized features"""
        print("Preparing ultra features...")

        self.train_features = self.engineer_ultra_features(self.train_df, is_train=True)
        self.test_features = self.engineer_ultra_features(self.test_df, is_train=False)

        exclude_cols = ['doj', 'final_seatcount', 'route_key', 'route', 'reverse_route']
        self.feature_columns = [col for col in self.train_features.columns if col not in exclude_cols]

        print(f"Ultra features: {len(self.feature_columns)}")

        self.X_train = self.train_features[self.feature_columns].fillna(0)
        self.y_train = self.train_features['final_seatcount']
        self.X_test = self.test_features[self.feature_columns].fillna(0)

        return self

    def train_ultra_models(self):
        """Train ultra-optimized models"""
        print("Training ultra models...")

        # 1. Extra Trees - BEST performer (ultra-tuned)
        print("Training Ultra Extra Trees...")
        et = ExtraTreesRegressor(
            n_estimators=400,
            max_depth=30,
            min_samples_split=2,
            min_samples_leaf=1,
            max_features='sqrt',
            random_state=42,
            n_jobs=-1
        )
        et.fit(self.X_train, self.y_train)
        self.models['et'] = et

        # 2. Random Forest - Ultra-tuned
        print("Training Ultra Random Forest...")
        rf = RandomForestRegressor(
            n_estimators=400,
            max_depth=30,
            min_samples_split=2,
            min_samples_leaf=1,
            max_features='sqrt',
            random_state=42,
            n_jobs=-1
        )
        rf.fit(self.X_train, self.y_train)
        self.models['rf'] = rf

        # 3. LightGBM - Ultra-tuned
        print("Training Ultra LightGBM...")
        lgb_model = lgb.LGBMRegressor(
            n_estimators=500,
            max_depth=20,
            learning_rate=0.02,
            subsample=0.85,
            colsample_bytree=0.85,
            reg_alpha=0.05,
            reg_lambda=0.05,
            min_child_samples=5,
            random_state=42,
            verbose=-1
        )
        lgb_model.fit(self.X_train, self.y_train)
        self.models['lgb'] = lgb_model

        # 4. XGBoost - Ultra-tuned
        print("Training Ultra XGBoost...")
        xgb_model = xgb.XGBRegressor(
            n_estimators=500,
            max_depth=15,
            learning_rate=0.02,
            subsample=0.85,
            colsample_bytree=0.85,
            reg_alpha=0.05,
            reg_lambda=0.05,
            random_state=42,
            verbosity=0
        )
        xgb_model.fit(self.X_train, self.y_train)
        self.models['xgb'] = xgb_model

        # 5. Gradient Boosting - Ultra-tuned
        print("Training Ultra Gradient Boosting...")
        gb = GradientBoostingRegressor(
            n_estimators=400,
            max_depth=20,
            learning_rate=0.02,
            subsample=0.85,
            random_state=42
        )
        gb.fit(self.X_train, self.y_train)
        self.models['gb'] = gb

        # 6. Ridge with scaling - For stability
        print("Training Ultra Ridge...")
        scaler = RobustScaler()
        X_train_scaled = scaler.fit_transform(self.X_train)
        self.scaler = scaler

        ridge = Ridge(alpha=1.0, random_state=42)
        ridge.fit(X_train_scaled, self.y_train)
        self.models['ridge'] = ridge

        return self

    def validate_ultra_models(self):
        """Ultra validation"""
        print("Ultra validation...")

        tscv = TimeSeriesSplit(n_splits=5)

        for name, model in self.models.items():
            scores = []
            for train_idx, val_idx in tscv.split(self.X_train):
                X_train_fold = self.X_train.iloc[train_idx]
                y_train_fold = self.y_train.iloc[train_idx]
                X_val_fold = self.X_train.iloc[val_idx]
                y_val_fold = self.y_train.iloc[val_idx]

                if name == 'ridge':
                    scaler = RobustScaler()
                    X_train_proc = scaler.fit_transform(X_train_fold)
                    X_val_proc = scaler.transform(X_val_fold)
                else:
                    X_train_proc = X_train_fold
                    X_val_proc = X_val_fold

                fold_model = type(model)(**model.get_params())
                fold_model.fit(X_train_proc, y_train_fold)
                pred = fold_model.predict(X_val_proc)

                rmse = np.sqrt(mean_squared_error(y_val_fold, pred))
                scores.append(rmse)

            print(f"🎯 {name.upper()}: {np.mean(scores):.2f} (+/- {np.std(scores) * 2:.2f})")

        return self

    def make_ultra_predictions(self):
        """Make ultra-optimized predictions"""
        print("Making ultra predictions...")

        predictions = {}
        for name, model in self.models.items():
            if name == 'ridge':
                X_test_proc = self.scaler.transform(self.X_test)
            else:
                X_test_proc = self.X_test

            pred = model.predict(X_test_proc)
            predictions[name] = pred
            print(f"🎯 {name.upper()}: {pred.mean():.2f} (std: {pred.std():.2f})")

        # Ultra-optimized ensemble weights (based on validation performance)
        weights = {'et': 0.35, 'rf': 0.25, 'lgb': 0.2, 'xgb': 0.15, 'gb': 0.03, 'ridge': 0.02}

        final_pred = np.zeros(len(self.X_test))
        for name, weight in weights.items():
            final_pred += weight * predictions[name]

        # Ultra post-processing
        final_pred = self._ultra_post_process(final_pred)

        print(f"🏆 ULTRA FINAL: {final_pred.mean():.2f} (std: {final_pred.std():.2f})")

        return final_pred

    def _ultra_post_process(self, predictions):
        """Ultra post-processing"""
        print("Ultra post-processing...")

        # Advanced outlier detection
        Q1 = np.percentile(predictions, 25)
        Q3 = np.percentile(predictions, 75)
        IQR = Q3 - Q1

        # Conservative clipping
        lower_bound = max(0, Q1 - 1.0 * IQR)
        upper_bound = Q3 + 1.5 * IQR

        predictions = np.clip(predictions, lower_bound, upper_bound)

        # Ensure realistic bounds
        predictions = np.clip(predictions, 0, 10000)

        # Smooth extreme predictions
        z_scores = np.abs((predictions - np.mean(predictions)) / np.std(predictions))
        extreme_mask = z_scores > 2.5
        if extreme_mask.sum() > 0:
            median_pred = np.median(predictions)
            predictions[extreme_mask] = predictions[extreme_mask] * 0.8 + median_pred * 0.2

        return predictions

    def create_submission(self, predictions):
        """Create ultra submission"""
        print("Creating ultra submission...")

        submission = self.sample_submission.copy()
        submission['final_seatcount'] = predictions.round(1)
        submission['final_seatcount'] = submission['final_seatcount'].clip(lower=0)

        submission.to_csv('sub600_submission.csv', index=False)
        print("🏆 Ultra submission saved: sub600_submission.csv")

        return submission

    def run_ultra_pipeline(self):
        """Run ultra pipeline"""
        print("="*70)
        print("🎯 SUB-600 ULTRA SOLUTION")
        print("🔥 TARGET: Sub-600 Score")
        print("="*70)

        self.load_data()
        self.create_holiday_calendar()
        self.prepare_ultra_features()
        self.train_ultra_models()
        self.validate_ultra_models()

        predictions = self.make_ultra_predictions()
        submission = self.create_submission(predictions)

        print("="*70)
        print("🚀 ULTRA PIPELINE COMPLETED!")
        print("🎯 TARGET: SUB-600 SCORE!")
        print("="*70)

        return submission

def main():
    solution = Sub600Solution()
    submission = solution.run_ultra_pipeline()

    print(f"\n🎯 SUB-600 ULTRA SUMMARY:")
    print(f"📊 Mean: {submission['final_seatcount'].mean():.2f}")
    print(f"📊 Median: {submission['final_seatcount'].median():.2f}")
    print(f"📊 Std: {submission['final_seatcount'].std():.2f}")
    print(f"📊 Range: {submission['final_seatcount'].min():.2f} - {submission['final_seatcount'].max():.2f}")
    print(f"\n🏆 READY FOR SUB-600 BREAKTHROUGH! 🏆")

if __name__ == "__main__":
    main()
