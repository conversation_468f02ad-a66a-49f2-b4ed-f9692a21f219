#!/usr/bin/env python3
"""
Bus Demand Prediction Solution
Analytics Vidhya Hackathon

This solution predicts bus journey demand 15 days before the journey date.
Key features:
- Temporal feature engineering
- Holiday calendar integration
- Route-specific patterns
- Ensemble modeling approach
- Advanced feature engineering
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.metrics import mean_squared_error
from sklearn.preprocessing import LabelEncoder
import lightgbm as lgb
import xgboost as xgb

class BusDemandPredictor:
    def __init__(self):
        self.models = {}
        self.feature_columns = []
        self.route_stats = {}
        self.label_encoders = {}
        
    def load_data(self):
        """Load training and test datasets"""
        print("Loading data...")
        
        # Load training data
        self.train_df = pd.read_csv('train_JDXlpm8/train/train.csv')
        print(f"Training data shape: {self.train_df.shape}")
        
        # Load test data
        self.test_df = pd.read_csv('test_8gqdJqH.csv')
        print(f"Test data shape: {self.test_df.shape}")
        
        # Load sample submission
        self.sample_submission = pd.read_csv('sample_submission_TQv3O0x.csv')
        
        return self
    
    def create_holiday_calendar(self):
        """Create Indian holiday calendar for 2023-2025"""
        print("Creating holiday calendar...")
        
        # Major Indian holidays (approximate dates - in practice would use exact calendar)
        holidays_2023 = [
            '2023-01-26',  # Republic Day
            '2023-03-08',  # Holi
            '2023-04-14',  # Ram Navami
            '2023-04-22',  # Good Friday
            '2023-05-01',  # Labour Day
            '2023-08-15',  # Independence Day
            '2023-09-19',  # Ganesh Chaturthi
            '2023-10-02',  # Gandhi Jayanti
            '2023-10-24',  # Dussehra
            '2023-11-13',  # Diwali
            '2023-12-25',  # Christmas
        ]
        
        holidays_2024 = [
            '2024-01-26',  # Republic Day
            '2024-03-25',  # Holi
            '2024-04-17',  # Ram Navami
            '2024-03-29',  # Good Friday
            '2024-05-01',  # Labour Day
            '2024-08-15',  # Independence Day
            '2024-09-07',  # Ganesh Chaturthi
            '2024-10-02',  # Gandhi Jayanti
            '2024-10-12',  # Dussehra
            '2024-11-01',  # Diwali
            '2024-12-25',  # Christmas
        ]
        
        holidays_2025 = [
            '2025-01-26',  # Republic Day
            '2025-03-14',  # Holi
            '2025-04-06',  # Ram Navami
            '2025-04-18',  # Good Friday
            '2025-05-01',  # Labour Day
            '2025-08-15',  # Independence Day
            '2025-08-27',  # Ganesh Chaturthi
            '2025-10-02',  # Gandhi Jayanti
            '2025-10-02',  # Dussehra
            '2025-10-20',  # Diwali
            '2025-12-25',  # Christmas
        ]
        
        all_holidays = holidays_2023 + holidays_2024 + holidays_2025
        self.holidays = pd.to_datetime(all_holidays)
        
        return self
    
    def engineer_features(self, df, is_train=True):
        """Create comprehensive feature set"""
        print(f"Engineering features for {'training' if is_train else 'test'} data...")
        
        df = df.copy()
        df['doj'] = pd.to_datetime(df['doj'])
        
        # Basic temporal features
        df['year'] = df['doj'].dt.year
        df['month'] = df['doj'].dt.month
        df['day'] = df['doj'].dt.day
        df['dayofweek'] = df['doj'].dt.dayofweek
        df['dayofyear'] = df['doj'].dt.dayofyear
        df['week'] = df['doj'].dt.isocalendar().week
        df['quarter'] = df['doj'].dt.quarter
        
        # Advanced temporal features
        df['is_weekend'] = (df['dayofweek'] >= 5).astype(int)
        df['is_month_start'] = (df['day'] <= 5).astype(int)
        df['is_month_end'] = (df['day'] >= 25).astype(int)
        df['is_quarter_start'] = (df['day'] <= 5) & (df['month'].isin([1, 4, 7, 10])).astype(int)
        df['is_quarter_end'] = (df['day'] >= 25) & (df['month'].isin([3, 6, 9, 12])).astype(int)
        
        # Holiday features
        df['days_to_nearest_holiday'] = df['doj'].apply(self._days_to_nearest_holiday)
        df['is_holiday'] = df['doj'].isin(self.holidays).astype(int)
        df['is_pre_holiday'] = df['doj'].apply(lambda x: (x + timedelta(days=1)) in self.holidays.values).astype(int)
        df['is_post_holiday'] = df['doj'].apply(lambda x: (x - timedelta(days=1)) in self.holidays.values).astype(int)
        df['holiday_week'] = df['doj'].apply(lambda x: any(abs((x - h).days) <= 7 for h in self.holidays)).astype(int)
        
        # Route features
        df['route'] = df['srcid'].astype(str) + '_' + df['destid'].astype(str)
        df['reverse_route'] = df['destid'].astype(str) + '_' + df['srcid'].astype(str)
        
        # Encode categorical features
        for col in ['srcid', 'destid']:
            if is_train:
                self.label_encoders[col] = LabelEncoder()
                df[col] = self.label_encoders[col].fit_transform(df[col])
            else:
                df[col] = self.label_encoders[col].transform(df[col])
        
        # Seasonal features
        df['season'] = df['month'].map({12: 0, 1: 0, 2: 0,  # Winter
                                       3: 1, 4: 1, 5: 1,   # Spring
                                       6: 2, 7: 2, 8: 2,   # Summer
                                       9: 3, 10: 3, 11: 3}) # Autumn
        
        # Advanced features
        df['days_since_year_start'] = df['dayofyear']
        df['days_until_year_end'] = 365 - df['dayofyear']
        df['is_long_weekend'] = ((df['dayofweek'] == 4) & df['is_pre_holiday']) | \
                               ((df['dayofweek'] == 0) & df['is_post_holiday'])
        
        if is_train:
            # Calculate route statistics from training data
            self._calculate_route_stats(df)
        
        # Add route statistics
        df = self._add_route_features(df)
        
        return df
    
    def _days_to_nearest_holiday(self, date):
        """Calculate days to nearest holiday"""
        if len(self.holidays) == 0:
            return 365  # Default if no holidays
        
        days_diff = np.abs((self.holidays - date).days)
        return days_diff.min()
    
    def _calculate_route_stats(self, df):
        """Calculate route-specific statistics"""
        print("Calculating route statistics...")
        
        # Route-level statistics
        route_stats = df.groupby('route')['final_seatcount'].agg([
            'mean', 'std', 'median', 'min', 'max', 'count'
        ]).reset_index()
        route_stats.columns = ['route', 'route_mean', 'route_std', 'route_median', 
                              'route_min', 'route_max', 'route_count']
        
        # Day of week statistics per route
        dow_stats = df.groupby(['route', 'dayofweek'])['final_seatcount'].agg(['mean', 'std']).reset_index()
        dow_stats = dow_stats.pivot(index='route', columns='dayofweek', values=['mean', 'std'])
        dow_stats.columns = [f'route_dow_{int(col[1])}_{col[0]}' for col in dow_stats.columns]
        dow_stats = dow_stats.reset_index()
        
        # Month statistics per route
        month_stats = df.groupby(['route', 'month'])['final_seatcount'].agg(['mean', 'std']).reset_index()
        month_stats = month_stats.pivot(index='route', columns='month', values=['mean', 'std'])
        month_stats.columns = [f'route_month_{int(col[1])}_{col[0]}' for col in month_stats.columns]
        month_stats = month_stats.reset_index()
        
        # Holiday impact statistics
        holiday_stats = df.groupby(['route', 'is_holiday'])['final_seatcount'].mean().reset_index()
        holiday_stats = holiday_stats.pivot(index='route', columns='is_holiday', values='final_seatcount')
        holiday_stats.columns = [f'route_holiday_{int(col)}_mean' for col in holiday_stats.columns]
        holiday_stats = holiday_stats.reset_index()
        
        # Merge all route statistics
        self.route_stats = route_stats.merge(dow_stats, on='route', how='left')
        self.route_stats = self.route_stats.merge(month_stats, on='route', how='left')
        self.route_stats = self.route_stats.merge(holiday_stats, on='route', how='left')
        
        # Fill missing values with overall means
        for col in self.route_stats.columns:
            if col != 'route':
                self.route_stats[col] = self.route_stats[col].fillna(self.route_stats[col].mean())
    
    def _add_route_features(self, df):
        """Add route-specific features to dataframe"""
        df = df.merge(self.route_stats, on='route', how='left')
        
        # Fill missing route statistics with global means
        for col in self.route_stats.columns:
            if col != 'route' and col in df.columns:
                df[col] = df[col].fillna(df[col].mean())
        
        return df
    
    def prepare_features(self):
        """Prepare feature matrices for training"""
        print("Preparing feature matrices...")
        
        # Engineer features
        self.train_features = self.engineer_features(self.train_df, is_train=True)
        self.test_features = self.engineer_features(self.test_df, is_train=False)
        
        # Define feature columns (exclude target and identifiers)
        exclude_cols = ['doj', 'final_seatcount', 'route_key', 'route', 'reverse_route']
        self.feature_columns = [col for col in self.train_features.columns if col not in exclude_cols]
        
        print(f"Number of features: {len(self.feature_columns)}")
        
        # Prepare training matrices
        self.X_train = self.train_features[self.feature_columns].fillna(0)
        self.y_train = self.train_features['final_seatcount']
        
        # Prepare test matrix
        self.X_test = self.test_features[self.feature_columns].fillna(0)
        
        return self

    def train_models(self):
        """Train ensemble of models"""
        print("Training models...")

        # Random Forest
        print("Training Random Forest...")
        rf_model = RandomForestRegressor(
            n_estimators=200,
            max_depth=20,
            min_samples_split=10,
            min_samples_leaf=5,
            random_state=42,
            n_jobs=-1
        )
        rf_model.fit(self.X_train, self.y_train)
        self.models['rf'] = rf_model

        # LightGBM
        print("Training LightGBM...")
        lgb_model = lgb.LGBMRegressor(
            n_estimators=300,
            max_depth=12,
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            verbose=-1
        )
        lgb_model.fit(self.X_train, self.y_train)
        self.models['lgb'] = lgb_model

        # XGBoost
        print("Training XGBoost...")
        xgb_model = xgb.XGBRegressor(
            n_estimators=300,
            max_depth=12,
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42
        )
        xgb_model.fit(self.X_train, self.y_train)
        self.models['xgb'] = xgb_model

        # Gradient Boosting
        print("Training Gradient Boosting...")
        gb_model = GradientBoostingRegressor(
            n_estimators=200,
            max_depth=10,
            learning_rate=0.05,
            subsample=0.8,
            random_state=42
        )
        gb_model.fit(self.X_train, self.y_train)
        self.models['gb'] = gb_model

        return self

    def validate_models(self):
        """Validate models using time series cross-validation"""
        print("Validating models...")
        
        tscv = TimeSeriesSplit(n_splits=5)
        
        for name, model in self.models.items():
            scores = cross_val_score(
                model, self.X_train, self.y_train,
                cv=tscv,
                scoring='neg_root_mean_squared_error'
            )
            print(f"{name} RMSE: {-scores.mean():.2f} (+/- {scores.std() * 2:.2f})")
        
        return self

    def make_predictions(self):
        """Make predictions using ensemble of models"""
        print("Making predictions...")
        
        predictions = {}
        for name, model in self.models.items():
            predictions[name] = model.predict(self.X_test)
        
        # Ensemble predictions (simple average)
        final_predictions = np.mean([pred for pred in predictions.values()], axis=0)
        
        return final_predictions

    def create_submission(self, predictions):
        """Create submission file"""
        print("Creating submission file...")
        
        submission = self.sample_submission.copy()
        submission['final_seatcount'] = predictions
        
        # Ensure predictions are non-negative
        submission['final_seatcount'] = submission['final_seatcount'].clip(lower=0)
        
        # Round predictions to nearest integer
        submission['final_seatcount'] = submission['final_seatcount'].round().astype(int)
        
        submission.to_csv('submission.csv', index=False)
        print("Submission file created: submission.csv")
        
        return self

    def run_complete_pipeline(self):
        """Run complete prediction pipeline"""
        return (self
                .load_data()
                .create_holiday_calendar()
                .prepare_features()
                .train_models()
                .validate_models()
                .make_predictions()
                .create_submission(self.make_predictions()))

def main():
    """Main function to run the prediction pipeline"""
    predictor = BusDemandPredictor()
    predictor.run_complete_pipeline()

if __name__ == "__main__":
    main()
