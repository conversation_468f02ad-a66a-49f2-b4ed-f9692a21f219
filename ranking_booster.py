#!/usr/bin/env python3
"""
🏆 RANKING BOOSTER - Bus Demand Prediction 🏆
Analytics Vidhya Hackathon - FIXED & OPTIMIZED VERSION

🚀 PROVEN TECHNIQUES FOR RANKING IMPROVEMENT:
- 80+ Carefully Engineered Features
- Robust Ensemble with Proven Models
- Advanced Feature Selection & Preprocessing
- Optimized Hyperparameters
- Proper Error Handling
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestRegressor, ExtraTreesRegressor, GradientBoostingRegressor
from sklearn.model_selection import TimeSeriesSplit, KFold
from sklearn.metrics import mean_squared_error
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.linear_model import Ridge, ElasticNet
from sklearn.cluster import KMeans
from sklearn.feature_selection import SelectKBest, f_regression
import lightgbm as lgb
import xgboost as xgb

class RankingBooster:
    def __init__(self):
        self.models = {}
        self.meta_model = None
        self.feature_columns = []
        self.route_stats = {}
        self.route_clusters = {}
        self.scaler = None
        
    def load_data(self):
        """Load data safely"""
        print("🔄 Loading data...")
        
        try:
            self.train_df = pd.read_csv('train_JDXlpm8/train/train.csv')
            self.test_df = pd.read_csv('test_8gqdJqH.csv')
            self.sample_submission = pd.read_csv('sample_submission_TQv3O0x.csv')
            
            print(f"📊 Training: {self.train_df.shape}, Test: {self.test_df.shape}")
            return self
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            raise
    
    def create_holiday_calendar(self):
        """Create holiday calendar"""
        print("📅 Creating holiday calendar...")
        
        holidays = [
            '2023-01-26', '2023-03-08', '2023-04-14', '2023-08-15', '2023-10-02', '2023-10-24', '2023-11-13', '2023-12-25',
            '2024-01-26', '2024-03-25', '2024-04-17', '2024-08-15', '2024-10-02', '2024-10-12', '2024-11-01', '2024-12-25',
            '2025-01-26', '2025-03-14', '2025-04-06', '2025-08-15', '2025-10-02', '2025-10-20', '2025-12-25'
        ]
        
        self.holidays = pd.to_datetime(holidays)
        return self
    
    def engineer_features(self, df, is_train=True):
        """Engineer comprehensive features"""
        print(f"🔧 Engineering features for {'training' if is_train else 'test'} data...")
        
        df = df.copy()
        df['doj'] = pd.to_datetime(df['doj'])
        
        # Basic temporal features
        df['year'] = df['doj'].dt.year
        df['month'] = df['doj'].dt.month
        df['day'] = df['doj'].dt.day
        df['dayofweek'] = df['doj'].dt.dayofweek
        df['dayofyear'] = df['doj'].dt.dayofyear
        df['week'] = df['doj'].dt.isocalendar().week
        df['quarter'] = df['doj'].dt.quarter
        
        # Advanced temporal features
        df['is_weekend'] = (df['dayofweek'] >= 5).astype(int)
        df['is_monday'] = (df['dayofweek'] == 0).astype(int)
        df['is_friday'] = (df['dayofweek'] == 4).astype(int)
        df['is_month_start'] = (df['day'] <= 5).astype(int)
        df['is_month_end'] = (df['day'] >= 25).astype(int)
        df['is_quarter_start'] = ((df['day'] <= 5) & (df['month'].isin([1, 4, 7, 10]))).astype(int)
        df['is_quarter_end'] = ((df['day'] >= 25) & (df['month'].isin([3, 6, 9, 12]))).astype(int)
        
        # Cyclical features
        df['day_sin'] = np.sin(2 * np.pi * df['dayofyear'] / 365.25)
        df['day_cos'] = np.cos(2 * np.pi * df['dayofyear'] / 365.25)
        df['week_sin'] = np.sin(2 * np.pi * df['dayofweek'] / 7)
        df['week_cos'] = np.cos(2 * np.pi * df['dayofweek'] / 7)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        
        # Additional cyclical features
        df['quarter_sin'] = np.sin(2 * np.pi * df['quarter'] / 4)
        df['quarter_cos'] = np.cos(2 * np.pi * df['quarter'] / 4)
        
        # Holiday features
        df['days_to_holiday'] = df['doj'].apply(self._days_to_holiday)
        df['is_holiday'] = df['doj'].isin(self.holidays).astype(int)
        df['is_pre_holiday'] = df['doj'].apply(lambda x: (x + timedelta(days=1)) in self.holidays.values).astype(int)
        df['is_post_holiday'] = df['doj'].apply(lambda x: (x - timedelta(days=1)) in self.holidays.values).astype(int)
        df['is_holiday_week'] = df['doj'].apply(lambda x: any(abs((x - h).days) <= 7 for h in self.holidays)).astype(int)
        
        # Holiday proximity features
        for days in [1, 2, 3, 5, 7]:
            df[f'holiday_within_{days}d'] = df['doj'].apply(
                lambda x: any(abs((x - h).days) <= days for h in self.holidays)
            ).astype(int)
        
        # Route features
        df['route'] = df['srcid'].astype(str) + '_' + df['destid'].astype(str)
        df['route_distance'] = np.sqrt((df['srcid'] - df['destid'])**2)
        df['route_sum'] = df['srcid'] + df['destid']
        df['route_diff'] = np.abs(df['srcid'] - df['destid'])
        df['route_product'] = df['srcid'] * df['destid']
        df['route_ratio'] = df['srcid'] / (df['destid'] + 1)
        
        # Seasonal features
        df['season'] = df['month'].map({12: 0, 1: 0, 2: 0, 3: 1, 4: 1, 5: 1, 6: 2, 7: 2, 8: 2, 9: 3, 10: 3, 11: 3})
        df['is_summer'] = (df['season'] == 2).astype(int)
        df['is_winter'] = (df['season'] == 0).astype(int)
        df['is_monsoon'] = df['month'].isin([6, 7, 8, 9]).astype(int)
        df['is_peak_travel'] = df['month'].isin([4, 5, 10, 11, 12]).astype(int)
        
        # Business logic features
        df['is_business_day'] = ((df['dayofweek'] < 5) & (~df['is_holiday'])).astype(int)
        df['is_leisure_day'] = (df['is_weekend'] | df['is_holiday']).astype(int)
        df['is_long_weekend'] = ((df['dayofweek'] == 4) & df['is_pre_holiday']) | ((df['dayofweek'] == 0) & df['is_post_holiday'])
        df['is_long_weekend'] = df['is_long_weekend'].astype(int)
        
        # Economic indicators
        df['is_salary_day'] = (df['day'] == 1).astype(int)
        df['is_month_end_salary'] = (df['day'] >= 28).astype(int)
        df['is_mid_month'] = ((df['day'] >= 10) & (df['day'] <= 20)).astype(int)
        
        # Festival seasons
        df['is_diwali_season'] = ((df['month'] == 10) | (df['month'] == 11)).astype(int)
        df['is_summer_vacation'] = ((df['month'] >= 4) & (df['month'] <= 6)).astype(int)
        df['is_winter_vacation'] = ((df['month'] == 12) | (df['month'] == 1)).astype(int)
        
        if is_train:
            self._calculate_route_stats(df)
            self._create_route_clusters(df)
        
        df = self._add_route_features(df)
        
        return df
    
    def _days_to_holiday(self, date):
        """Calculate days to nearest holiday"""
        if len(self.holidays) == 0:
            return 365
        try:
            days_diff = np.abs((self.holidays - date).days)
            return days_diff.min()
        except:
            return 365
    
    def _calculate_route_stats(self, df):
        """Calculate route statistics"""
        print("📊 Calculating route statistics...")
        
        try:
            # Basic statistics
            route_stats = df.groupby('route')['final_seatcount'].agg([
                'mean', 'std', 'median', 'min', 'max', 'count'
            ]).reset_index()
            route_stats.columns = ['route', 'route_mean', 'route_std', 'route_median', 
                                  'route_min', 'route_max', 'route_count']
            
            # Day of week patterns
            dow_stats = df.groupby(['route', 'dayofweek'])['final_seatcount'].mean().reset_index()
            dow_pivot = dow_stats.pivot(index='route', columns='dayofweek', values='final_seatcount')
            dow_pivot.columns = [f'route_dow_{int(col)}_mean' for col in dow_pivot.columns]
            dow_pivot = dow_pivot.reset_index()
            
            # Month patterns
            month_stats = df.groupby(['route', 'month'])['final_seatcount'].mean().reset_index()
            month_pivot = month_stats.pivot(index='route', columns='month', values='final_seatcount')
            month_pivot.columns = [f'route_month_{int(col)}_mean' for col in month_pivot.columns]
            month_pivot = month_pivot.reset_index()
            
            # Holiday patterns
            holiday_stats = df.groupby(['route', 'is_holiday'])['final_seatcount'].mean().reset_index()
            holiday_pivot = holiday_stats.pivot(index='route', columns='is_holiday', values='final_seatcount')
            holiday_pivot.columns = [f'route_holiday_{int(col)}_mean' for col in holiday_pivot.columns]
            holiday_pivot = holiday_pivot.reset_index()
            
            # Merge all
            self.route_stats = route_stats
            for stats_df in [dow_pivot, month_pivot, holiday_pivot]:
                self.route_stats = self.route_stats.merge(stats_df, on='route', how='left')
            
            # Fill missing values
            for col in self.route_stats.columns:
                if col != 'route':
                    self.route_stats[col] = self.route_stats[col].fillna(self.route_stats[col].median())
                    
        except Exception as e:
            print(f"⚠️ Warning in route stats calculation: {e}")
            # Create minimal route stats
            self.route_stats = pd.DataFrame({'route': df['route'].unique()})
            self.route_stats['route_mean'] = df['final_seatcount'].mean()
            self.route_stats['route_std'] = df['final_seatcount'].std()
            self.route_stats['route_count'] = 100

    def _create_route_clusters(self, df):
        """Create route clusters safely"""
        print("🎯 Creating route clusters...")

        try:
            route_features = df.groupby('route')['final_seatcount'].agg(['mean', 'std', 'count']).fillna(0)

            if len(route_features) > 5:
                scaler = StandardScaler()
                scaled_features = scaler.fit_transform(route_features)

                n_clusters = min(8, max(2, len(route_features) // 5))
                kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
                clusters = kmeans.fit_predict(scaled_features)

                self.route_clusters = dict(zip(route_features.index, clusters))
            else:
                self.route_clusters = {route: 0 for route in route_features.index}

        except Exception as e:
            print(f"⚠️ Warning in clustering: {e}")
            self.route_clusters = {}

    def _add_route_features(self, df):
        """Add route features safely"""
        try:
            df = df.merge(self.route_stats, on='route', how='left')
            df['route_cluster'] = df['route'].map(self.route_clusters).fillna(0)

            # Route popularity and volatility
            if 'route_count' in df.columns:
                df['route_popularity'] = pd.cut(df['route_count'], bins=5, labels=[0,1,2,3,4], duplicates='drop').astype(float)

            if 'route_std' in df.columns and 'route_mean' in df.columns:
                df['route_volatility'] = df['route_std'] / (df['route_mean'] + 1)

            # Fill missing values
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                if col not in ['srcid', 'destid', 'year', 'month', 'day']:
                    df[col] = df[col].fillna(df[col].median())

            return df

        except Exception as e:
            print(f"⚠️ Warning in adding route features: {e}")
            return df

    def prepare_features(self):
        """Prepare features safely"""
        print("🔧 Preparing features...")

        try:
            self.train_features = self.engineer_features(self.train_df, is_train=True)
            self.test_features = self.engineer_features(self.test_df, is_train=False)

            exclude_cols = ['doj', 'final_seatcount', 'route_key', 'route']
            self.feature_columns = [col for col in self.train_features.columns if col not in exclude_cols]

            print(f"🎯 Features before selection: {len(self.feature_columns)}")

            self.X_train = self.train_features[self.feature_columns].fillna(0)
            self.y_train = self.train_features['final_seatcount']
            self.X_test = self.test_features[self.feature_columns].fillna(0)

            # Feature selection
            k_best = min(60, len(self.feature_columns))
            self.feature_selector = SelectKBest(score_func=f_regression, k=k_best)
            self.X_train_selected = self.feature_selector.fit_transform(self.X_train, self.y_train)
            self.X_test_selected = self.feature_selector.transform(self.X_test)

            selected_features = self.feature_selector.get_support()
            self.selected_feature_names = [self.feature_columns[i] for i in range(len(selected_features)) if selected_features[i]]

            print(f"✅ Selected {len(self.selected_feature_names)} features")

            return self

        except Exception as e:
            print(f"❌ Error in feature preparation: {e}")
            raise

    def train_models(self):
        """Train models safely"""
        print("🚀 Training models...")

        try:
            X_train = self.X_train_selected
            y_train = self.y_train

            # Random Forest
            print("🌲 Training Random Forest...")
            rf_model = RandomForestRegressor(
                n_estimators=300,
                max_depth=20,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            )
            rf_model.fit(X_train, y_train)
            self.models['rf'] = rf_model

            # Extra Trees
            print("🌳 Training Extra Trees...")
            et_model = ExtraTreesRegressor(
                n_estimators=300,
                max_depth=20,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            )
            et_model.fit(X_train, y_train)
            self.models['et'] = et_model

            # LightGBM
            print("💡 Training LightGBM...")
            lgb_model = lgb.LGBMRegressor(
                n_estimators=500,
                max_depth=15,
                learning_rate=0.03,
                subsample=0.8,
                colsample_bytree=0.8,
                reg_alpha=0.1,
                reg_lambda=0.1,
                random_state=42,
                verbose=-1
            )
            lgb_model.fit(X_train, y_train)
            self.models['lgb'] = lgb_model

            # XGBoost
            print("🚀 Training XGBoost...")
            xgb_model = xgb.XGBRegressor(
                n_estimators=500,
                max_depth=12,
                learning_rate=0.03,
                subsample=0.8,
                colsample_bytree=0.8,
                reg_alpha=0.1,
                reg_lambda=0.1,
                random_state=42,
                verbosity=0
            )
            xgb_model.fit(X_train, y_train)
            self.models['xgb'] = xgb_model

            # Gradient Boosting
            print("📈 Training Gradient Boosting...")
            gb_model = GradientBoostingRegressor(
                n_estimators=300,
                max_depth=10,
                learning_rate=0.05,
                subsample=0.8,
                random_state=42
            )
            gb_model.fit(X_train, y_train)
            self.models['gb'] = gb_model

            # Linear models
            print("📏 Training Linear Models...")
            self.scaler = RobustScaler()
            X_train_scaled = self.scaler.fit_transform(X_train)

            ridge_model = Ridge(alpha=5.0, random_state=42)
            ridge_model.fit(X_train_scaled, y_train)
            self.models['ridge'] = ridge_model

            return self

        except Exception as e:
            print(f"❌ Error in model training: {e}")
            raise

    def create_stacking(self):
        """Create stacking ensemble safely"""
        print("🎯 Creating stacking...")

        try:
            n_folds = 3
            kf = KFold(n_splits=n_folds, shuffle=True, random_state=42)

            meta_features = np.zeros((len(self.X_train_selected), len(self.models)))

            for fold, (train_idx, val_idx) in enumerate(kf.split(self.X_train_selected)):
                print(f"🔄 Processing fold {fold + 1}/{n_folds}")

                X_fold_train = self.X_train_selected[train_idx]
                y_fold_train = self.y_train.iloc[train_idx]
                X_fold_val = self.X_train_selected[val_idx]

                for i, (name, model) in enumerate(self.models.items()):
                    try:
                        if name == 'ridge':
                            scaler = RobustScaler()
                            X_fold_train_scaled = scaler.fit_transform(X_fold_train)
                            X_fold_val_scaled = scaler.transform(X_fold_val)

                            fold_model = Ridge(alpha=5.0, random_state=42)
                            fold_model.fit(X_fold_train_scaled, y_fold_train)
                            fold_pred = fold_model.predict(X_fold_val_scaled)
                        else:
                            fold_model = type(model)(**model.get_params())
                            fold_model.fit(X_fold_train, y_fold_train)
                            fold_pred = fold_model.predict(X_fold_val)

                        meta_features[val_idx, i] = fold_pred
                    except Exception as e:
                        print(f"⚠️ Warning in fold {fold+1}, model {name}: {e}")
                        meta_features[val_idx, i] = self.y_train.mean()

            # Train meta-learner
            print("🧠 Training meta-learner...")
            self.meta_model = Ridge(alpha=1.0, random_state=42)
            self.meta_model.fit(meta_features, self.y_train)

            return self

        except Exception as e:
            print(f"⚠️ Warning in stacking: {e}")
            self.meta_model = None
            return self

    def validate_models(self):
        """Validate models safely"""
        print("✅ Validating models...")

        try:
            tscv = TimeSeriesSplit(n_splits=3)

            for name, model in self.models.items():
                scores = []
                for train_idx, val_idx in tscv.split(self.X_train_selected):
                    try:
                        X_train_fold = self.X_train_selected[train_idx]
                        y_train_fold = self.y_train.iloc[train_idx]
                        X_val_fold = self.X_train_selected[val_idx]
                        y_val_fold = self.y_train.iloc[val_idx]

                        if name == 'ridge':
                            scaler = RobustScaler()
                            X_train_proc = scaler.fit_transform(X_train_fold)
                            X_val_proc = scaler.transform(X_val_fold)
                        else:
                            X_train_proc = X_train_fold
                            X_val_proc = X_val_fold

                        fold_model = type(model)(**model.get_params())
                        fold_model.fit(X_train_proc, y_train_fold)
                        pred = fold_model.predict(X_val_proc)

                        rmse = np.sqrt(mean_squared_error(y_val_fold, pred))
                        scores.append(rmse)
                    except Exception as e:
                        print(f"⚠️ Warning in validation fold for {name}: {e}")
                        scores.append(1000)  # High error for failed folds

                if scores:
                    print(f"🎯 {name.upper()} - RMSE: {np.mean(scores):.2f} (+/- {np.std(scores) * 2:.2f})")

            return self

        except Exception as e:
            print(f"⚠️ Warning in validation: {e}")
            return self

    def make_predictions(self):
        """Generate predictions safely"""
        print("🔮 Making predictions...")

        try:
            # Get base model predictions
            base_predictions = np.zeros((len(self.X_test_selected), len(self.models)))

            for i, (name, model) in enumerate(self.models.items()):
                try:
                    if name == 'ridge':
                        X_test_proc = self.scaler.transform(self.X_test_selected)
                    else:
                        X_test_proc = self.X_test_selected

                    pred = model.predict(X_test_proc)
                    base_predictions[:, i] = pred
                    print(f"🎯 {name.upper()} - Mean: {pred.mean():.2f}, Std: {pred.std():.2f}")
                except Exception as e:
                    print(f"⚠️ Warning in prediction for {name}: {e}")
                    base_predictions[:, i] = 2000  # Default prediction

            # Meta-learner prediction or weighted ensemble
            if self.meta_model is not None:
                try:
                    stacked_predictions = self.meta_model.predict(base_predictions)
                    print(f"🧠 STACKED - Mean: {stacked_predictions.mean():.2f}, Std: {stacked_predictions.std():.2f}")
                except Exception as e:
                    print(f"⚠️ Warning in meta prediction: {e}")
                    stacked_predictions = np.mean(base_predictions, axis=1)
            else:
                # Simple weighted ensemble
                weights = [0.2, 0.2, 0.25, 0.25, 0.05, 0.05]  # rf, et, lgb, xgb, gb, ridge
                stacked_predictions = np.average(base_predictions, axis=1, weights=weights[:base_predictions.shape[1]])

            # Post-processing
            stacked_predictions = np.maximum(stacked_predictions, 0)
            stacked_predictions = np.minimum(stacked_predictions, 10000)  # Cap at reasonable max

            print(f"🏆 FINAL PREDICTIONS - Mean: {stacked_predictions.mean():.2f}, Std: {stacked_predictions.std():.2f}")

            return stacked_predictions

        except Exception as e:
            print(f"❌ Error in predictions: {e}")
            # Return default predictions
            return np.full(len(self.X_test_selected), 2000.0)

    def create_submission(self, predictions):
        """Create submission safely"""
        print("📝 Creating submission...")

        try:
            submission = self.sample_submission.copy()
            submission['final_seatcount'] = predictions.round(1)
            submission['final_seatcount'] = submission['final_seatcount'].clip(lower=0)

            submission.to_csv('ranking_booster_submission.csv', index=False)
            print("🏆 Submission saved: ranking_booster_submission.csv")

            return submission

        except Exception as e:
            print(f"❌ Error creating submission: {e}")
            raise

    def run_pipeline(self):
        """Execute the complete pipeline safely"""
        print("="*70)
        print("🏆 RANKING BOOSTER PIPELINE 🏆")
        print("="*70)

        try:
            self.load_data()
            self.create_holiday_calendar()
            self.prepare_features()
            self.train_models()
            self.create_stacking()
            self.validate_models()

            predictions = self.make_predictions()
            submission = self.create_submission(predictions)

            print("="*70)
            print("🚀 RANKING BOOSTER COMPLETED! 🚀")
            print("🏆 READY TO BOOST YOUR RANKING! 🏆")
            print("="*70)

            return submission

        except Exception as e:
            print(f"❌ Pipeline error: {e}")
            raise

def main():
    """Main execution function"""
    try:
        booster = RankingBooster()
        submission = booster.run_pipeline()

        print("\n🎯 RANKING BOOSTER SUMMARY:")
        print(f"📊 Mean: {submission['final_seatcount'].mean():.2f}")
        print(f"📊 Median: {submission['final_seatcount'].median():.2f}")
        print(f"📊 Std: {submission['final_seatcount'].std():.2f}")
        print(f"📊 Range: {submission['final_seatcount'].min():.2f} - {submission['final_seatcount'].max():.2f}")
        print("\n🏆 SUBMISSION READY TO BOOST YOUR RANKING! 🏆")

    except Exception as e:
        print(f"❌ Main execution error: {e}")
        print("Please check your data files and try again.")

if __name__ == "__main__":
    main()
