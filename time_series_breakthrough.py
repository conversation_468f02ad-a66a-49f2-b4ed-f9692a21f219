#!/usr/bin/env python3
"""
🚀 TIME SERIES BREAKTHROUGH SOLUTION 🚀
Bus Demand Prediction - TARGET: Sub-600 Score

🔥 REVOLUTIONARY APPROACH FOR TIME SERIES EXTRAPOLATION
- Training: 2023-2024 → Test: 2025 (1+ year gap!)
- Time series decomposition and trend extrapolation
- Route-specific seasonal patterns
- Advanced temporal modeling
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error
import lightgbm as lgb

class TimeSeriesBreakthrough:
    def __init__(self):
        self.route_models = {}
        self.global_trend = None
        self.seasonal_patterns = {}
        self.route_baselines = {}
        
    def load_data(self):
        """Load data"""
        print("Loading data...")
        
        self.train_df = pd.read_csv('train_JDXlpm8/train/train.csv')
        self.test_df = pd.read_csv('test_8gqdJqH.csv')
        self.sample_submission = pd.read_csv('sample_submission_TQv3O0x.csv')
        
        # Convert dates
        self.train_df['doj'] = pd.to_datetime(self.train_df['doj'])
        self.test_df['doj'] = pd.to_datetime(self.test_df['doj'])
        
        # Create route column
        self.train_df['route'] = self.train_df['srcid'].astype(str) + '_' + self.train_df['destid'].astype(str)
        self.test_df['route'] = self.test_df['srcid'].astype(str) + '_' + self.test_df['destid'].astype(str)
        
        print(f"Training: {self.train_df.shape} ({self.train_df['doj'].min()} to {self.train_df['doj'].max()})")
        print(f"Test: {self.test_df.shape} ({self.test_df['doj'].min()} to {self.test_df['doj'].max()})")
        print(f"⚠️ TIME GAP: {(self.test_df['doj'].min() - self.train_df['doj'].max()).days} days!")
        
        return self
    
    def analyze_time_patterns(self):
        """Analyze time series patterns"""
        print("🔍 Analyzing time series patterns...")
        
        # Create time features
        self.train_df['year'] = self.train_df['doj'].dt.year
        self.train_df['month'] = self.train_df['doj'].dt.month
        self.train_df['dayofweek'] = self.train_df['doj'].dt.dayofweek
        self.train_df['dayofyear'] = self.train_df['doj'].dt.dayofyear
        self.train_df['days_since_start'] = (self.train_df['doj'] - self.train_df['doj'].min()).dt.days
        
        # Same for test
        self.test_df['year'] = self.test_df['doj'].dt.year
        self.test_df['month'] = self.test_df['doj'].dt.month
        self.test_df['dayofweek'] = self.test_df['doj'].dt.dayofweek
        self.test_df['dayofyear'] = self.test_df['doj'].dt.dayofyear
        self.test_df['days_since_start'] = (self.test_df['doj'] - self.train_df['doj'].min()).dt.days
        
        # Analyze overall trends
        daily_totals = self.train_df.groupby('doj')['final_seatcount'].sum().reset_index()
        daily_totals['days_since_start'] = (daily_totals['doj'] - daily_totals['doj'].min()).dt.days
        
        # Fit global trend
        self.global_trend = LinearRegression()
        self.global_trend.fit(daily_totals[['days_since_start']], daily_totals['final_seatcount'])
        
        trend_slope = self.global_trend.coef_[0]
        print(f"📈 Global trend: {trend_slope:.2f} seats/day")
        
        return self
    
    def extract_seasonal_patterns(self):
        """Extract seasonal patterns for each route"""
        print("🌊 Extracting seasonal patterns...")
        
        for route in self.train_df['route'].unique():
            route_data = self.train_df[self.train_df['route'] == route].copy()
            
            if len(route_data) < 10:  # Skip routes with too little data
                continue
            
            # Calculate baseline (median)
            baseline = route_data['final_seatcount'].median()
            self.route_baselines[route] = baseline
            
            # Day of week pattern
            dow_pattern = route_data.groupby('dayofweek')['final_seatcount'].median()
            dow_pattern = dow_pattern / baseline  # Normalize
            
            # Month pattern
            month_pattern = route_data.groupby('month')['final_seatcount'].median()
            month_pattern = month_pattern / baseline  # Normalize
            
            # Store patterns
            self.seasonal_patterns[route] = {
                'dow': dow_pattern.to_dict(),
                'month': month_pattern.to_dict(),
                'baseline': baseline
            }
        
        print(f"✅ Extracted patterns for {len(self.seasonal_patterns)} routes")
        return self
    
    def build_route_models(self):
        """Build time series models for each major route"""
        print("🏗️ Building route-specific models...")
        
        # Get major routes (top 50% by volume)
        route_volumes = self.train_df.groupby('route')['final_seatcount'].sum()
        major_routes = route_volumes.nlargest(int(len(route_volumes) * 0.5)).index
        
        for route in major_routes:
            route_data = self.train_df[self.train_df['route'] == route].copy()
            
            if len(route_data) < 20:
                continue
            
            # Create features for this route
            route_data = route_data.sort_values('doj')
            
            # Time-based features
            features = ['days_since_start', 'month', 'dayofweek', 'dayofyear']
            
            # Cyclical features
            route_data['month_sin'] = np.sin(2 * np.pi * route_data['month'] / 12)
            route_data['month_cos'] = np.cos(2 * np.pi * route_data['month'] / 12)
            route_data['dow_sin'] = np.sin(2 * np.pi * route_data['dayofweek'] / 7)
            route_data['dow_cos'] = np.cos(2 * np.pi * route_data['dayofweek'] / 7)
            route_data['year_sin'] = np.sin(2 * np.pi * route_data['dayofyear'] / 365)
            route_data['year_cos'] = np.cos(2 * np.pi * route_data['dayofyear'] / 365)
            
            features.extend(['month_sin', 'month_cos', 'dow_sin', 'dow_cos', 'year_sin', 'year_cos'])
            
            # Lag features (if enough data)
            if len(route_data) > 50:
                route_data = route_data.sort_values('doj')
                route_data['lag_7'] = route_data['final_seatcount'].shift(7)
                route_data['lag_30'] = route_data['final_seatcount'].shift(30)
                route_data['rolling_7'] = route_data['final_seatcount'].rolling(7, min_periods=1).mean()
                route_data['rolling_30'] = route_data['final_seatcount'].rolling(30, min_periods=1).mean()
                
                features.extend(['lag_7', 'lag_30', 'rolling_7', 'rolling_30'])
                route_data = route_data.dropna()
            
            if len(route_data) < 10:
                continue
            
            # Train model for this route
            X = route_data[features].fillna(route_data[features].median())
            y = route_data['final_seatcount']
            
            # Use LightGBM for time series
            model = lgb.LGBMRegressor(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.05,
                random_state=42,
                verbose=-1
            )
            model.fit(X, y)
            
            self.route_models[route] = {
                'model': model,
                'features': features,
                'baseline': y.median()
            }
        
        print(f"✅ Built models for {len(self.route_models)} major routes")
        return self
    
    def predict_with_extrapolation(self):
        """Make predictions with time series extrapolation"""
        print("🔮 Making time series predictions...")
        
        predictions = []
        
        for _, row in self.test_df.iterrows():
            route = row['route']
            
            # Method 1: Route-specific model (if available)
            if route in self.route_models:
                try:
                    model_info = self.route_models[route]
                    model = model_info['model']
                    features = model_info['features']
                    
                    # Create feature vector
                    feature_dict = {
                        'days_since_start': row['days_since_start'],
                        'month': row['month'],
                        'dayofweek': row['dayofweek'],
                        'dayofyear': row['dayofyear'],
                        'month_sin': np.sin(2 * np.pi * row['month'] / 12),
                        'month_cos': np.cos(2 * np.pi * row['month'] / 12),
                        'dow_sin': np.sin(2 * np.pi * row['dayofweek'] / 7),
                        'dow_cos': np.cos(2 * np.pi * row['dayofweek'] / 7),
                        'year_sin': np.sin(2 * np.pi * row['dayofyear'] / 365),
                        'year_cos': np.cos(2 * np.pi * row['dayofyear'] / 365),
                    }
                    
                    # Add lag features (use baseline for missing)
                    if 'lag_7' in features:
                        baseline = model_info['baseline']
                        feature_dict.update({
                            'lag_7': baseline,
                            'lag_30': baseline,
                            'rolling_7': baseline,
                            'rolling_30': baseline
                        })
                    
                    X_pred = pd.DataFrame([feature_dict])[features].fillna(model_info['baseline'])
                    pred = model.predict(X_pred)[0]
                    
                    # Apply trend extrapolation
                    trend_adjustment = self.global_trend.predict([[row['days_since_start']]])[0] / 100000  # Scale down
                    pred = pred * (1 + trend_adjustment / 10000)  # Small trend adjustment
                    
                    predictions.append(max(0, pred))
                    continue
                    
                except Exception as e:
                    pass
            
            # Method 2: Seasonal pattern extrapolation
            if route in self.seasonal_patterns:
                try:
                    pattern = self.seasonal_patterns[route]
                    baseline = pattern['baseline']
                    
                    # Apply seasonal adjustments
                    dow_adj = pattern['dow'].get(row['dayofweek'], 1.0)
                    month_adj = pattern['month'].get(row['month'], 1.0)
                    
                    # Combine adjustments
                    seasonal_pred = baseline * dow_adj * month_adj
                    
                    # Apply global trend
                    days_diff = row['days_since_start']
                    trend_factor = 1 + (self.global_trend.coef_[0] * days_diff / 1000000)  # Scale trend
                    
                    pred = seasonal_pred * trend_factor
                    predictions.append(max(0, pred))
                    continue
                    
                except Exception as e:
                    pass
            
            # Method 3: Global fallback
            # Use overall median with seasonal adjustments
            overall_median = self.train_df['final_seatcount'].median()
            
            # Day of week adjustment
            dow_medians = self.train_df.groupby('dayofweek')['final_seatcount'].median()
            dow_adj = dow_medians.get(row['dayofweek'], overall_median) / overall_median
            
            # Month adjustment
            month_medians = self.train_df.groupby('month')['final_seatcount'].median()
            month_adj = month_medians.get(row['month'], overall_median) / overall_median
            
            pred = overall_median * dow_adj * month_adj
            predictions.append(max(0, pred))
        
        predictions = np.array(predictions)
        
        # Final post-processing
        predictions = np.clip(predictions, 0, 8000)  # Reasonable bounds
        
        print(f"🏆 Predictions: Mean={predictions.mean():.2f}, Std={predictions.std():.2f}")
        print(f"📊 Range: {predictions.min():.2f} - {predictions.max():.2f}")
        
        return predictions
    
    def create_submission(self, predictions):
        """Create submission"""
        submission = self.sample_submission.copy()
        submission['final_seatcount'] = predictions.round(1)
        submission['final_seatcount'] = submission['final_seatcount'].clip(lower=0)
        
        submission.to_csv('time_series_breakthrough_submission.csv', index=False)
        print("🏆 Time series submission saved!")
        
        return submission
    
    def run_pipeline(self):
        """Run time series pipeline"""
        print("="*70)
        print("🚀 TIME SERIES BREAKTHROUGH SOLUTION")
        print("🎯 TARGET: Sub-600 Score with Time Series Extrapolation")
        print("="*70)
        
        self.load_data()
        self.analyze_time_patterns()
        self.extract_seasonal_patterns()
        self.build_route_models()
        
        predictions = self.predict_with_extrapolation()
        submission = self.create_submission(predictions)
        
        print("="*70)
        print("🚀 TIME SERIES BREAKTHROUGH COMPLETED!")
        print("🎯 REVOLUTIONARY APPROACH FOR 2025 EXTRAPOLATION!")
        print("="*70)
        
        return submission

def main():
    solution = TimeSeriesBreakthrough()
    submission = solution.run_pipeline()
    
    print(f"\n🎯 TIME SERIES BREAKTHROUGH SUMMARY:")
    print(f"📊 Mean: {submission['final_seatcount'].mean():.2f}")
    print(f"📊 Median: {submission['final_seatcount'].median():.2f}")
    print(f"📊 Std: {submission['final_seatcount'].std():.2f}")
    print(f"📊 Range: {submission['final_seatcount'].min():.2f} - {submission['final_seatcount'].max():.2f}")
    print(f"\n🏆 READY FOR SUB-600 BREAKTHROUGH! 🏆")

if __name__ == "__main__":
    main()
