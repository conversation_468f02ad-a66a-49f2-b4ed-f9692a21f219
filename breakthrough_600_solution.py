#!/usr/bin/env python3
"""
🎯 BREAKTHROUGH 600 SOLUTION 🎯
Bus Demand Prediction - TARGET: Sub-600 Score

🔥 FOCUSED ON WHAT WORKS
- Proven features from winning solution
- Optimized hyperparameters
- Fast execution
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestRegressor, ExtraTreesRegressor
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_squared_error
import lightgbm as lgb
import xgboost as xgb

class Breakthrough600Solution:
    def __init__(self):
        self.models = {}
        self.route_stats = {}
        
    def load_data(self):
        """Load data"""
        print("Loading data...")
        
        self.train_df = pd.read_csv('train_JDXlpm8/train/train.csv')
        self.test_df = pd.read_csv('test_8gqdJqH.csv')
        self.sample_submission = pd.read_csv('sample_submission_TQv3O0x.csv')
        
        print(f"Training: {self.train_df.shape}, Test: {self.test_df.shape}")
        return self
    
    def create_holidays(self):
        """Key holidays"""
        holidays = [
            '2023-01-26', '2023-03-08', '2023-04-14', '2023-08-15', '2023-10-02', '2023-11-13', '2023-12-25',
            '2024-01-26', '2024-03-25', '2024-04-17', '2024-08-15', '2024-10-02', '2024-11-01', '2024-12-25',
            '2025-01-26', '2025-03-14', '2025-04-06', '2025-08-15', '2025-10-02', '2025-10-20', '2025-12-25'
        ]
        self.holidays = pd.to_datetime(holidays)
        return self
    
    def engineer_features(self, df, is_train=True):
        """Focused feature engineering"""
        print(f"Engineering features for {'training' if is_train else 'test'} data...")
        
        df = df.copy()
        df['doj'] = pd.to_datetime(df['doj'])
        
        # Core temporal
        df['year'] = df['doj'].dt.year
        df['month'] = df['doj'].dt.month
        df['day'] = df['doj'].dt.day
        df['dayofweek'] = df['doj'].dt.dayofweek
        df['dayofyear'] = df['doj'].dt.dayofyear
        df['week'] = df['doj'].dt.isocalendar().week
        df['quarter'] = df['doj'].dt.quarter
        
        # Key indicators
        df['is_weekend'] = (df['dayofweek'] >= 5).astype(int)
        df['is_friday'] = (df['dayofweek'] == 4).astype(int)
        df['is_monday'] = (df['dayofweek'] == 0).astype(int)
        df['is_month_start'] = (df['day'] <= 5).astype(int)
        df['is_month_end'] = (df['day'] >= 25).astype(int)
        df['is_quarter_start'] = ((df['day'] <= 5) & (df['month'].isin([1, 4, 7, 10]))).astype(int)
        df['is_quarter_end'] = ((df['day'] >= 25) & (df['month'].isin([3, 6, 9, 12]))).astype(int)
        
        # Optimized cyclical features
        df['day_sin'] = np.sin(2 * np.pi * df['dayofyear'] / 365.25)
        df['day_cos'] = np.cos(2 * np.pi * df['dayofyear'] / 365.25)
        df['week_sin'] = np.sin(2 * np.pi * df['dayofweek'] / 7)
        df['week_cos'] = np.cos(2 * np.pi * df['dayofweek'] / 7)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        
        # Additional cyclical harmonics
        df['week_sin2'] = np.sin(4 * np.pi * df['dayofweek'] / 7)
        df['week_cos2'] = np.cos(4 * np.pi * df['dayofweek'] / 7)
        df['month_sin2'] = np.sin(4 * np.pi * df['month'] / 12)
        df['month_cos2'] = np.cos(4 * np.pi * df['month'] / 12)
        
        # Holiday features
        df['days_to_holiday'] = df['doj'].apply(self._days_to_holiday)
        df['is_holiday'] = df['doj'].isin(self.holidays).astype(int)
        df['is_pre_holiday'] = df['doj'].apply(lambda x: (x + timedelta(days=1)) in self.holidays.values).astype(int)
        df['is_post_holiday'] = df['doj'].apply(lambda x: (x - timedelta(days=1)) in self.holidays.values).astype(int)
        df['holiday_week'] = df['doj'].apply(lambda x: any(abs((x - h).days) <= 7 for h in self.holidays)).astype(int)
        
        # Holiday proximity
        df['holiday_within_3d'] = (df['days_to_holiday'] <= 3).astype(int)
        df['holiday_within_7d'] = (df['days_to_holiday'] <= 7).astype(int)
        
        # Route features
        df['route'] = df['srcid'].astype(str) + '_' + df['destid'].astype(str)
        df['route_distance'] = np.sqrt((df['srcid'] - df['destid'])**2)
        df['route_sum'] = df['srcid'] + df['destid']
        df['route_diff'] = np.abs(df['srcid'] - df['destid'])
        df['route_product'] = df['srcid'] * df['destid']
        df['route_ratio'] = df['srcid'] / (df['destid'] + 1)
        
        # Seasonal
        df['season'] = df['month'].map({12: 0, 1: 0, 2: 0, 3: 1, 4: 1, 5: 1, 6: 2, 7: 2, 8: 2, 9: 3, 10: 3, 11: 3})
        df['is_summer'] = (df['season'] == 2).astype(int)
        df['is_winter'] = (df['season'] == 0).astype(int)
        df['is_monsoon'] = df['month'].isin([6, 7, 8, 9]).astype(int)
        df['is_peak_travel'] = df['month'].isin([4, 5, 10, 11, 12]).astype(int)
        
        # Business
        df['is_business_day'] = ((df['dayofweek'] < 5) & (~df['is_holiday'])).astype(int)
        df['is_leisure_time'] = (df['is_weekend'] | df['is_holiday']).astype(int)
        df['is_long_weekend'] = ((df['dayofweek'] == 4) & df['is_pre_holiday']) | ((df['dayofweek'] == 0) & df['is_post_holiday'])
        df['is_long_weekend'] = df['is_long_weekend'].astype(int)
        
        # Economic
        df['is_month_end_salary'] = (df['day'] >= 28).astype(int)
        df['is_month_start_spending'] = (df['day'] <= 5).astype(int)
        
        if is_train:
            self._calculate_route_stats(df)
        
        df = self._add_route_features(df)
        
        return df
    
    def _days_to_holiday(self, date):
        """Days to nearest holiday"""
        if len(self.holidays) == 0:
            return 365
        return np.abs((self.holidays - date).days).min()
    
    def _calculate_route_stats(self, df):
        """Calculate route statistics"""
        print("Calculating route statistics...")
        
        # Basic route stats
        route_stats = df.groupby('route')['final_seatcount'].agg([
            'mean', 'std', 'median', 'min', 'max', 'count'
        ]).reset_index()
        route_stats.columns = ['route', 'route_mean', 'route_std', 'route_median', 'route_min', 'route_max', 'route_count']
        
        # Day of week patterns
        dow_stats = df.groupby(['route', 'dayofweek'])['final_seatcount'].mean().reset_index()
        dow_pivot = dow_stats.pivot(index='route', columns='dayofweek', values='final_seatcount')
        dow_pivot.columns = [f'route_dow_{int(col)}_mean' for col in dow_pivot.columns]
        dow_pivot = dow_pivot.reset_index()
        
        # Weekend patterns
        weekend_stats = df.groupby(['route', 'is_weekend'])['final_seatcount'].mean().reset_index()
        weekend_pivot = weekend_stats.pivot(index='route', columns='is_weekend', values='final_seatcount')
        weekend_pivot.columns = [f'route_weekend_{int(col)}_mean' for col in weekend_pivot.columns]
        weekend_pivot = weekend_pivot.reset_index()
        
        # Merge
        self.route_stats = route_stats.merge(dow_pivot, on='route', how='left')
        self.route_stats = self.route_stats.merge(weekend_pivot, on='route', how='left')
        
        # Fill missing
        for col in self.route_stats.columns:
            if col != 'route':
                self.route_stats[col] = self.route_stats[col].fillna(self.route_stats[col].median())
    
    def _add_route_features(self, df):
        """Add route features"""
        df = df.merge(self.route_stats, on='route', how='left')
        
        # Route volatility
        if 'route_std' in df.columns and 'route_mean' in df.columns:
            df['route_volatility'] = df['route_std'] / (df['route_mean'] + 1)
        
        # Fill missing
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            if col not in ['srcid', 'destid', 'year', 'month', 'day']:
                df[col] = df[col].fillna(df[col].median())
        
        return df
    
    def prepare_features(self):
        """Prepare features"""
        print("Preparing features...")
        
        self.train_features = self.engineer_features(self.train_df, is_train=True)
        self.test_features = self.engineer_features(self.test_df, is_train=False)
        
        exclude_cols = ['doj', 'final_seatcount', 'route']
        self.feature_columns = [col for col in self.train_features.columns if col not in exclude_cols]
        
        print(f"Features: {len(self.feature_columns)}")
        
        self.X_train = self.train_features[self.feature_columns].fillna(0)
        self.y_train = self.train_features['final_seatcount']
        self.X_test = self.test_features[self.feature_columns].fillna(0)
        
        return self
    
    def train_models(self):
        """Train breakthrough models"""
        print("Training breakthrough models...")
        
        # Extra Trees - BEST performer with ultra tuning
        print("Training Breakthrough Extra Trees...")
        et = ExtraTreesRegressor(
            n_estimators=500,  # Increased
            max_depth=35,      # Increased
            min_samples_split=2,
            min_samples_leaf=1,
            max_features='sqrt',
            random_state=42,
            n_jobs=4  # Limit cores
        )
        et.fit(self.X_train, self.y_train)
        self.models['et'] = et
        
        # Random Forest - Ultra tuned
        print("Training Breakthrough Random Forest...")
        rf = RandomForestRegressor(
            n_estimators=500,  # Increased
            max_depth=35,      # Increased
            min_samples_split=2,
            min_samples_leaf=1,
            max_features='sqrt',
            random_state=42,
            n_jobs=4  # Limit cores
        )
        rf.fit(self.X_train, self.y_train)
        self.models['rf'] = rf
        
        # LightGBM - Ultra tuned
        print("Training Breakthrough LightGBM...")
        lgb_model = lgb.LGBMRegressor(
            n_estimators=600,  # Increased
            max_depth=20,      # Increased
            learning_rate=0.02, # Decreased
            subsample=0.9,
            colsample_bytree=0.9,
            reg_alpha=0.01,
            reg_lambda=0.01,
            min_child_samples=3,
            random_state=42,
            verbose=-1
        )
        lgb_model.fit(self.X_train, self.y_train)
        self.models['lgb'] = lgb_model
        
        # XGBoost - Ultra tuned
        print("Training Breakthrough XGBoost...")
        xgb_model = xgb.XGBRegressor(
            n_estimators=600,  # Increased
            max_depth=18,      # Increased
            learning_rate=0.02, # Decreased
            subsample=0.9,
            colsample_bytree=0.9,
            reg_alpha=0.01,
            reg_lambda=0.01,
            random_state=42,
            verbosity=0
        )
        xgb_model.fit(self.X_train, self.y_train)
        self.models['xgb'] = xgb_model
        
        return self
    
    def validate_models(self):
        """Validate models"""
        print("Validating breakthrough models...")
        
        tscv = TimeSeriesSplit(n_splits=5)
        
        for name, model in self.models.items():
            scores = []
            for train_idx, val_idx in tscv.split(self.X_train):
                X_train_fold = self.X_train.iloc[train_idx]
                y_train_fold = self.y_train.iloc[train_idx]
                X_val_fold = self.X_train.iloc[val_idx]
                y_val_fold = self.y_train.iloc[val_idx]
                
                fold_model = type(model)(**model.get_params())
                fold_model.fit(X_train_fold, y_train_fold)
                pred = fold_model.predict(X_val_fold)
                
                rmse = np.sqrt(mean_squared_error(y_val_fold, pred))
                scores.append(rmse)
            
            print(f"🎯 {name.upper()}: {np.mean(scores):.2f} (+/- {np.std(scores) * 2:.2f})")
        
        return self
    
    def make_predictions(self):
        """Make breakthrough predictions"""
        print("Making breakthrough predictions...")
        
        predictions = {}
        for name, model in self.models.items():
            pred = model.predict(self.X_test)
            predictions[name] = pred
            print(f"🎯 {name.upper()}: {pred.mean():.2f}")
        
        # Breakthrough ensemble weights (ET was best)
        weights = {'et': 0.45, 'rf': 0.25, 'lgb': 0.18, 'xgb': 0.12}
        
        final_pred = np.zeros(len(self.X_test))
        for name, weight in weights.items():
            final_pred += weight * predictions[name]
        
        # Conservative post-processing
        final_pred = np.clip(final_pred, 0, 8000)
        
        print(f"🏆 BREAKTHROUGH FINAL: {final_pred.mean():.2f}")
        
        return final_pred
    
    def create_submission(self, predictions):
        """Create submission"""
        submission = self.sample_submission.copy()
        submission['final_seatcount'] = predictions.round(1)
        submission['final_seatcount'] = submission['final_seatcount'].clip(lower=0)
        
        submission.to_csv('breakthrough_600_submission.csv', index=False)
        print("🏆 Breakthrough submission saved: breakthrough_600_submission.csv")
        
        return submission
    
    def run_pipeline(self):
        """Run breakthrough pipeline"""
        print("="*70)
        print("🎯 BREAKTHROUGH 600 SOLUTION")
        print("🔥 TARGET: Sub-600 Score")
        print("="*70)
        
        self.load_data()
        self.create_holidays()
        self.prepare_features()
        self.train_models()
        self.validate_models()
        
        predictions = self.make_predictions()
        submission = self.create_submission(predictions)
        
        print("="*70)
        print("🚀 BREAKTHROUGH 600 COMPLETED!")
        print("="*70)
        
        return submission

def main():
    solution = Breakthrough600Solution()
    submission = solution.run_pipeline()
    
    print(f"\n🎯 BREAKTHROUGH 600 SUMMARY:")
    print(f"📊 Mean: {submission['final_seatcount'].mean():.2f}")
    print(f"📊 Median: {submission['final_seatcount'].median():.2f}")
    print(f"📊 Range: {submission['final_seatcount'].min():.2f} - {submission['final_seatcount'].max():.2f}")
    print(f"\n🏆 READY FOR SUB-600 BREAKTHROUGH! 🏆")

if __name__ == "__main__":
    main()
