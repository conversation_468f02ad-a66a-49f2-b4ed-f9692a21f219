#!/usr/bin/env python3
"""
Deep Data Analysis to understand why others score 520
"""

import pandas as pd
import numpy as np

def analyze_data():
    print("🔍 DEEP DATA ANALYSIS")
    print("="*50)
    
    # Load data
    train = pd.read_csv('train_JDXlpm8/train/train.csv')
    test = pd.read_csv('test_8gqdJqH.csv')
    
    print('=== TRAINING DATA ANALYSIS ===')
    print(f'Shape: {train.shape}')
    print(f'Date range: {train["doj"].min()} to {train["doj"].max()}')
    print(f'Target stats:')
    print(f'  Mean: {train["final_seatcount"].mean():.2f}')
    print(f'  Median: {train["final_seatcount"].median():.2f}')
    print(f'  Std: {train["final_seatcount"].std():.2f}')
    print(f'  Min: {train["final_seatcount"].min():.2f}')
    print(f'  Max: {train["final_seatcount"].max():.2f}')
    print(f'  25%: {train["final_seatcount"].quantile(0.25):.2f}')
    print(f'  75%: {train["final_seatcount"].quantile(0.75):.2f}')
    
    print('\n=== TEST DATA ANALYSIS ===')
    print(f'Shape: {test.shape}')
    print(f'Date range: {test["doj"].min()} to {test["doj"].max()}')
    
    print('\n=== ROUTE ANALYSIS ===')
    train['route'] = train['srcid'].astype(str) + '_' + train['destid'].astype(str)
    test['route'] = test['srcid'].astype(str) + '_' + test['destid'].astype(str)
    
    train_routes = set(train['route'].unique())
    test_routes = set(test['route'].unique())
    
    print(f'Training routes: {len(train_routes)}')
    print(f'Test routes: {len(test_routes)}')
    print(f'Common routes: {len(train_routes & test_routes)}')
    print(f'New routes in test: {len(test_routes - train_routes)}')
    
    if len(test_routes - train_routes) > 0:
        print('NEW ROUTES:', list(test_routes - train_routes)[:10])
    
    print('\n=== TOP ROUTES BY VOLUME ===')
    route_volumes = train.groupby('route')['final_seatcount'].agg(['mean', 'count', 'sum']).sort_values('sum', ascending=False)
    print(route_volumes.head(10))
    
    print('\n=== TEMPORAL PATTERNS ===')
    train['doj'] = pd.to_datetime(train['doj'])
    test['doj'] = pd.to_datetime(test['doj'])
    
    # Day of week patterns
    train['dayofweek'] = train['doj'].dt.dayofweek
    dow_pattern = train.groupby('dayofweek')['final_seatcount'].mean()
    print('Day of week pattern:')
    for dow, avg in dow_pattern.items():
        day_name = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'][dow]
        print(f'  {day_name}: {avg:.2f}')
    
    # Month patterns
    train['month'] = train['doj'].dt.month
    month_pattern = train.groupby('month')['final_seatcount'].mean()
    print('\nMonth pattern:')
    for month, avg in month_pattern.items():
        print(f'  Month {month}: {avg:.2f}')
    
    print('\n=== DISTRIBUTION ANALYSIS ===')
    # Check for zero values
    zero_count = (train['final_seatcount'] == 0).sum()
    print(f'Zero values: {zero_count} ({zero_count/len(train)*100:.2f}%)')
    
    # Check distribution
    print('Value distribution:')
    print(f'  0-100: {((train["final_seatcount"] >= 0) & (train["final_seatcount"] <= 100)).sum()}')
    print(f'  100-500: {((train["final_seatcount"] > 100) & (train["final_seatcount"] <= 500)).sum()}')
    print(f'  500-1000: {((train["final_seatcount"] > 500) & (train["final_seatcount"] <= 1000)).sum()}')
    print(f'  1000-2000: {((train["final_seatcount"] > 1000) & (train["final_seatcount"] <= 2000)).sum()}')
    print(f'  2000-5000: {((train["final_seatcount"] > 2000) & (train["final_seatcount"] <= 5000)).sum()}')
    print(f'  5000+: {(train["final_seatcount"] > 5000).sum()}')
    
    print('\n=== POTENTIAL INSIGHTS ===')
    
    # Check if most values are actually low
    low_values = train['final_seatcount'] <= 1000
    print(f'Values <= 1000: {low_values.sum()} ({low_values.sum()/len(train)*100:.2f}%)')
    
    # Check median vs mean difference
    median_val = train['final_seatcount'].median()
    mean_val = train['final_seatcount'].mean()
    print(f'Median vs Mean: {median_val:.2f} vs {mean_val:.2f} (diff: {mean_val - median_val:.2f})')
    
    # This suggests the distribution is right-skewed
    if mean_val > median_val * 1.5:
        print('⚠️ HIGHLY RIGHT-SKEWED DISTRIBUTION!')
        print('💡 INSIGHT: Maybe predicting median instead of mean would be better!')
        print('💡 INSIGHT: Maybe log transformation would help!')
        print('💡 INSIGHT: Maybe most predictions should be much lower!')

if __name__ == "__main__":
    analyze_data()
