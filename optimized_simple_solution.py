#!/usr/bin/env python3
"""
🎯 OPTIMIZED SIMPLE SOLUTION 🎯
Bus Demand Prediction - BACK TO BASICS FOR BETTER SCORE

🔥 FOCUS: SIMPLE BUT EFFECTIVE
- Proven features that work
- Optimized hyperparameters
- Clean ensemble without over-engineering
- Target: Beat 1120 score
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestRegressor, ExtraTreesRegressor
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_squared_error
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import Ridge
import lightgbm as lgb
import xgboost as xgb

class OptimizedSimpleSolution:
    def __init__(self):
        self.models = {}
        self.feature_columns = []
        self.route_stats = {}
        
    def load_data(self):
        """Load data"""
        print("🔄 Loading data...")
        
        self.train_df = pd.read_csv('train_JDXlpm8/train/train.csv')
        self.test_df = pd.read_csv('test_8gqdJqH.csv')
        self.sample_submission = pd.read_csv('sample_submission_TQv3O0x.csv')
        
        print(f"📊 Training: {self.train_df.shape}, Test: {self.test_df.shape}")
        return self
    
    def create_simple_holidays(self):
        """Simple but effective holiday list"""
        print("📅 Creating holiday calendar...")
        
        holidays = [
            # 2023
            '2023-01-26', '2023-03-08', '2023-04-14', '2023-08-15', 
            '2023-10-02', '2023-10-24', '2023-11-13', '2023-12-25',
            # 2024
            '2024-01-26', '2024-03-25', '2024-04-17', '2024-08-15',
            '2024-10-02', '2024-10-12', '2024-11-01', '2024-12-25',
            # 2025
            '2025-01-26', '2025-03-14', '2025-04-06', '2025-08-15',
            '2025-10-02', '2025-10-20', '2025-12-25'
        ]
        
        self.holidays = pd.to_datetime(holidays)
        return self
    
    def engineer_simple_features(self, df, is_train=True):
        """Simple but effective feature engineering"""
        print(f"🔧 Engineering features for {'training' if is_train else 'test'} data...")
        
        df = df.copy()
        df['doj'] = pd.to_datetime(df['doj'])
        
        # === CORE TEMPORAL FEATURES ===
        df['year'] = df['doj'].dt.year
        df['month'] = df['doj'].dt.month
        df['day'] = df['doj'].dt.day
        df['dayofweek'] = df['doj'].dt.dayofweek
        df['dayofyear'] = df['doj'].dt.dayofyear
        df['week'] = df['doj'].dt.isocalendar().week
        df['quarter'] = df['doj'].dt.quarter
        
        # === SIMPLE CYCLICAL FEATURES ===
        # Weekly pattern
        df['dow_sin'] = np.sin(2 * np.pi * df['dayofweek'] / 7)
        df['dow_cos'] = np.cos(2 * np.pi * df['dayofweek'] / 7)
        
        # Monthly pattern
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        
        # Yearly pattern
        df['year_sin'] = np.sin(2 * np.pi * df['dayofyear'] / 365)
        df['year_cos'] = np.cos(2 * np.pi * df['dayofyear'] / 365)
        
        # === SIMPLE HOLIDAY FEATURES ===
        df['is_holiday'] = df['doj'].isin(self.holidays).astype(int)
        df['days_to_holiday'] = df['doj'].apply(self._days_to_holiday)
        df['is_pre_holiday'] = df['doj'].apply(lambda x: (x + timedelta(days=1)) in self.holidays.values).astype(int)
        df['is_post_holiday'] = df['doj'].apply(lambda x: (x - timedelta(days=1)) in self.holidays.values).astype(int)
        
        # Holiday proximity
        df['holiday_within_3d'] = df['doj'].apply(lambda x: any(abs((x - h).days) <= 3 for h in self.holidays)).astype(int)
        df['holiday_within_7d'] = df['doj'].apply(lambda x: any(abs((x - h).days) <= 7 for h in self.holidays)).astype(int)
        
        # === SIMPLE TEMPORAL FEATURES ===
        df['is_weekend'] = (df['dayofweek'] >= 5).astype(int)
        df['is_monday'] = (df['dayofweek'] == 0).astype(int)
        df['is_friday'] = (df['dayofweek'] == 4).astype(int)
        df['is_month_start'] = (df['day'] <= 5).astype(int)
        df['is_month_end'] = (df['day'] >= 25).astype(int)
        
        # === ROUTE FEATURES ===
        df['route'] = df['srcid'].astype(str) + '_' + df['destid'].astype(str)
        df['route_distance'] = np.sqrt((df['srcid'] - df['destid'])**2)
        df['route_sum'] = df['srcid'] + df['destid']
        df['route_diff'] = np.abs(df['srcid'] - df['destid'])
        
        # === SEASONAL FEATURES ===
        df['season'] = df['month'].map({12: 0, 1: 0, 2: 0, 3: 1, 4: 1, 5: 1, 6: 2, 7: 2, 8: 2, 9: 3, 10: 3, 11: 3})
        df['is_summer'] = (df['season'] == 2).astype(int)
        df['is_winter'] = (df['season'] == 0).astype(int)
        df['is_monsoon'] = df['month'].isin([6, 7, 8, 9]).astype(int)
        
        # === BUSINESS FEATURES ===
        df['is_business_day'] = ((df['dayofweek'] < 5) & (~df['is_holiday'])).astype(int)
        df['is_leisure_day'] = (df['is_weekend'] | df['is_holiday']).astype(int)
        
        if is_train:
            self._calculate_simple_route_stats(df)
        
        df = self._add_simple_route_features(df)
        
        return df
    
    def _days_to_holiday(self, date):
        """Days to nearest holiday"""
        if len(self.holidays) == 0:
            return 365
        return np.abs((self.holidays - date).days).min()
    
    def _calculate_simple_route_stats(self, df):
        """Calculate simple route statistics"""
        print("📊 Calculating route statistics...")
        
        # Basic route statistics
        route_stats = df.groupby('route')['final_seatcount'].agg([
            'mean', 'std', 'median', 'count'
        ]).reset_index()
        route_stats.columns = ['route', 'route_mean', 'route_std', 'route_median', 'route_count']
        
        # Day of week patterns (simplified)
        dow_stats = df.groupby(['route', 'dayofweek'])['final_seatcount'].mean().reset_index()
        dow_pivot = dow_stats.pivot(index='route', columns='dayofweek', values='final_seatcount')
        dow_pivot.columns = [f'route_dow_{int(col)}_mean' for col in dow_pivot.columns]
        dow_pivot = dow_pivot.reset_index()
        
        # Weekend vs weekday
        weekend_stats = df.groupby(['route', 'is_weekend'])['final_seatcount'].mean().reset_index()
        weekend_pivot = weekend_stats.pivot(index='route', columns='is_weekend', values='final_seatcount')
        weekend_pivot.columns = [f'route_weekend_{int(col)}_mean' for col in weekend_pivot.columns]
        weekend_pivot = weekend_pivot.reset_index()
        
        # Merge
        self.route_stats = route_stats
        for stats_df in [dow_pivot, weekend_pivot]:
            self.route_stats = self.route_stats.merge(stats_df, on='route', how='left')
        
        # Fill missing values
        for col in self.route_stats.columns:
            if col != 'route':
                self.route_stats[col] = self.route_stats[col].fillna(self.route_stats[col].median())
    
    def _add_simple_route_features(self, df):
        """Add simple route features"""
        # Add route statistics
        df = df.merge(self.route_stats, on='route', how='left')
        
        # Route volatility
        if 'route_std' in df.columns and 'route_mean' in df.columns:
            df['route_volatility'] = df['route_std'] / (df['route_mean'] + 1)
        
        # Fill missing values
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            if col not in ['srcid', 'destid', 'year', 'month', 'day']:
                df[col] = df[col].fillna(df[col].median())
        
        return df
    
    def prepare_features(self):
        """Prepare features"""
        print("🔧 Preparing features...")
        
        self.train_features = self.engineer_simple_features(self.train_df, is_train=True)
        self.test_features = self.engineer_simple_features(self.test_df, is_train=False)
        
        exclude_cols = ['doj', 'final_seatcount', 'route']
        self.feature_columns = [col for col in self.train_features.columns if col not in exclude_cols]
        
        print(f"🎯 Total features: {len(self.feature_columns)}")
        
        self.X_train = self.train_features[self.feature_columns].fillna(0)
        self.y_train = self.train_features['final_seatcount']
        self.X_test = self.test_features[self.feature_columns].fillna(0)
        
        return self
    
    def train_optimized_models(self):
        """Train optimized models"""
        print("🚀 Training optimized models...")
        
        X_train = self.X_train
        y_train = self.y_train
        
        # 1. LightGBM (primary model)
        print("💡 Training LightGBM...")
        lgb_model = lgb.LGBMRegressor(
            n_estimators=300,
            max_depth=8,
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            reg_alpha=0.1,
            reg_lambda=0.1,
            random_state=42,
            verbose=-1
        )
        lgb_model.fit(X_train, y_train)
        self.models['lgb'] = lgb_model
        
        # 2. XGBoost (secondary model)
        print("🚀 Training XGBoost...")
        xgb_model = xgb.XGBRegressor(
            n_estimators=300,
            max_depth=6,
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            reg_alpha=0.1,
            reg_lambda=0.1,
            random_state=42,
            verbosity=0
        )
        xgb_model.fit(X_train, y_train)
        self.models['xgb'] = xgb_model
        
        # 3. Random Forest (for diversity)
        print("🌲 Training Random Forest...")
        rf_model = RandomForestRegressor(
            n_estimators=200,
            max_depth=15,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42,
            n_jobs=-1
        )
        rf_model.fit(X_train, y_train)
        self.models['rf'] = rf_model
        
        print(f"✅ Trained {len(self.models)} models")
        return self
    
    def validate_models(self):
        """Simple validation"""
        print("✅ Validating models...")
        
        tscv = TimeSeriesSplit(n_splits=3)
        
        for name, model in self.models.items():
            scores = []
            for train_idx, val_idx in tscv.split(self.X_train):
                X_train_fold = self.X_train.iloc[train_idx]
                y_train_fold = self.y_train.iloc[train_idx]
                X_val_fold = self.X_train.iloc[val_idx]
                y_val_fold = self.y_train.iloc[val_idx]
                
                fold_model = type(model)(**model.get_params())
                fold_model.fit(X_train_fold, y_train_fold)
                pred = fold_model.predict(X_val_fold)
                
                rmse = np.sqrt(mean_squared_error(y_val_fold, pred))
                scores.append(rmse)
            
            print(f"🎯 {name.upper()}: {np.mean(scores):.2f} (+/- {np.std(scores) * 2:.2f})")
        
        return self
    
    def make_predictions(self):
        """Make simple ensemble predictions"""
        print("🔮 Making predictions...")
        
        # Get predictions from each model
        predictions = {}
        for name, model in self.models.items():
            pred = model.predict(self.X_test)
            predictions[name] = pred
            print(f"🎯 {name.upper()}: {pred.mean():.2f} (std: {pred.std():.2f})")
        
        # Simple weighted ensemble (optimized weights)
        weights = {'lgb': 0.5, 'xgb': 0.35, 'rf': 0.15}
        
        final_predictions = np.zeros(len(self.X_test))
        for name, weight in weights.items():
            final_predictions += weight * predictions[name]
        
        # Simple post-processing
        final_predictions = np.clip(final_predictions, 0, 15000)
        
        print(f"🏆 FINAL: {final_predictions.mean():.2f} (std: {final_predictions.std():.2f})")
        
        return final_predictions
    
    def create_submission(self, predictions):
        """Create submission"""
        print("📝 Creating submission...")
        
        submission = self.sample_submission.copy()
        submission['final_seatcount'] = predictions.round(1)
        submission['final_seatcount'] = submission['final_seatcount'].clip(lower=0)
        
        submission.to_csv('optimized_simple_submission.csv', index=False)
        print("🏆 Submission saved: optimized_simple_submission.csv")
        
        return submission
    
    def run_pipeline(self):
        """Execute the pipeline"""
        print("="*60)
        print("🎯 OPTIMIZED SIMPLE SOLUTION")
        print("🔥 BACK TO BASICS FOR BETTER SCORE")
        print("="*60)
        
        self.load_data()
        self.create_simple_holidays()
        self.prepare_features()
        self.train_optimized_models()
        self.validate_models()
        
        predictions = self.make_predictions()
        submission = self.create_submission(predictions)
        
        print("="*60)
        print("🚀 OPTIMIZED SOLUTION COMPLETED!")
        print("🎯 TARGET: BEAT 1120 SCORE!")
        print("="*60)
        
        return submission

def main():
    """Main execution"""
    solution = OptimizedSimpleSolution()
    submission = solution.run_pipeline()
    
    print(f"\n🎯 OPTIMIZED SOLUTION SUMMARY:")
    print(f"📊 Mean: {submission['final_seatcount'].mean():.2f}")
    print(f"📊 Median: {submission['final_seatcount'].median():.2f}")
    print(f"📊 Std: {submission['final_seatcount'].std():.2f}")
    print(f"📊 Range: {submission['final_seatcount'].min():.2f} - {submission['final_seatcount'].max():.2f}")
    print(f"\n🏆 READY TO BEAT 1120 SCORE! 🏆")

if __name__ == "__main__":
    main()
