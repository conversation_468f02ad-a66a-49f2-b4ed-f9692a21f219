#!/usr/bin/env python3
"""
🏆 TOP-50 ELITE SOLUTION - Bus Demand Prediction 🏆
Analytics Vidhya Hackathon - RANK 102 → TOP 50 BREAKTHROUGH

🚀 ELITE TECHNIQUES FOR TOP-50 BREAKTHROUGH:
- 200+ Ultra-Advanced Features with Deep Domain Knowledge
- 12-Model Super Ensemble with Neural Networks
- Advanced Time Series Decomposition & Fourier Analysis
- Multi-Level Target Encoding with Bayesian Smoothing
- Pseudo-Labeling & Semi-Supervised Learning
- Advanced Outlier Detection & Robust Preprocessing
- Hyperparameter Optimization with Optuna
- Multi-Stage Stacking with Blending & Meta-Meta Learning
- Route Similarity Networks & Graph Features
- Advanced Holiday Impact Modeling
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestRegressor, ExtraTreesRegressor, GradientBoostingRegressor
from sklearn.model_selection import TimeSeriesSplit, KFold, StratifiedKFold
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.preprocessing import StandardScaler, RobustScaler, QuantileTransformer, PowerTransformer
from sklearn.linear_model import Ridge, ElasticNet, Lasso, BayesianRidge, HuberRegressor
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.decomposition import PCA, TruncatedSVD, FastICA
from sklearn.feature_selection import SelectKBest, f_regression, RFE, SelectFromModel
from sklearn.neighbors import KNeighborsRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.svm import SVR
import lightgbm as lgb
import xgboost as xgb
try:
    import catboost as cb
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False

from scipy import stats
from scipy.stats import skew, kurtosis, boxcox
from scipy.signal import savgol_filter
from scipy.fft import fft, fftfreq
import itertools
from collections import defaultdict

class Top50EliteSolution:
    def __init__(self):
        self.models = {}
        self.meta_models = {}
        self.meta_meta_models = {}
        self.feature_columns = []
        self.route_stats = {}
        self.route_clusters = {}
        self.route_similarity = {}
        self.scalers = {}
        self.transformers = {}
        self.feature_selectors = {}
        self.outlier_detectors = {}
        self.pseudo_labels = {}
        
    def load_data(self):
        """Load data with advanced preprocessing"""
        print("🔄 Loading data with elite preprocessing...")
        
        self.train_df = pd.read_csv('train_JDXlpm8/train/train.csv')
        self.test_df = pd.read_csv('test_8gqdJqH.csv')
        self.sample_submission = pd.read_csv('sample_submission_TQv3O0x.csv')
        
        print(f"📊 Training: {self.train_df.shape}, Test: {self.test_df.shape}")
        
        # Advanced data quality analysis
        self._perform_elite_data_analysis()
        
        return self
    
    def _perform_elite_data_analysis(self):
        """Perform elite-level data analysis"""
        print("🔍 Performing elite data analysis...")
        
        # Outlier detection using multiple methods
        Q1 = self.train_df['final_seatcount'].quantile(0.25)
        Q3 = self.train_df['final_seatcount'].quantile(0.75)
        IQR = Q3 - Q1
        
        # Multiple outlier bounds
        self.outlier_bounds = {
            'iqr_lower': Q1 - 1.5 * IQR,
            'iqr_upper': Q3 + 1.5 * IQR,
            'zscore_threshold': 3,
            'percentile_lower': self.train_df['final_seatcount'].quantile(0.01),
            'percentile_upper': self.train_df['final_seatcount'].quantile(0.99)
        }
        
        # Flag different types of outliers
        self.train_df['is_iqr_outlier'] = (
            (self.train_df['final_seatcount'] < self.outlier_bounds['iqr_lower']) |
            (self.train_df['final_seatcount'] > self.outlier_bounds['iqr_upper'])
        ).astype(int)
        
        z_scores = np.abs(stats.zscore(self.train_df['final_seatcount']))
        self.train_df['is_zscore_outlier'] = (z_scores > self.outlier_bounds['zscore_threshold']).astype(int)
        
        self.train_df['is_extreme_outlier'] = (
            (self.train_df['final_seatcount'] < self.outlier_bounds['percentile_lower']) |
            (self.train_df['final_seatcount'] > self.outlier_bounds['percentile_upper'])
        ).astype(int)
        
        print(f"📈 IQR Outliers: {self.train_df['is_iqr_outlier'].sum()}")
        print(f"📈 Z-Score Outliers: {self.train_df['is_zscore_outlier'].sum()}")
        print(f"📈 Extreme Outliers: {self.train_df['is_extreme_outlier'].sum()}")
    
    def create_elite_holiday_calendar(self):
        """Create elite holiday calendar with advanced features"""
        print("📅 Creating elite holiday calendar...")
        
        # Comprehensive holiday data with multiple attributes
        self.holiday_data = {
            # 2023 - Major holidays with detailed attributes
            '2023-01-26': {'type': 'national', 'importance': 1.0, 'travel_impact': 0.9, 'duration': 1, 'name': 'Republic Day'},
            '2023-03-08': {'type': 'religious', 'importance': 0.9, 'travel_impact': 0.8, 'duration': 2, 'name': 'Holi'},
            '2023-04-14': {'type': 'religious', 'importance': 0.8, 'travel_impact': 0.7, 'duration': 1, 'name': 'Ram Navami'},
            '2023-04-22': {'type': 'religious', 'importance': 0.7, 'travel_impact': 0.6, 'duration': 1, 'name': 'Good Friday'},
            '2023-05-01': {'type': 'national', 'importance': 0.6, 'travel_impact': 0.5, 'duration': 1, 'name': 'Labour Day'},
            '2023-08-15': {'type': 'national', 'importance': 1.0, 'travel_impact': 0.9, 'duration': 1, 'name': 'Independence Day'},
            '2023-09-19': {'type': 'religious', 'importance': 0.9, 'travel_impact': 0.8, 'duration': 3, 'name': 'Ganesh Chaturthi'},
            '2023-10-02': {'type': 'national', 'importance': 1.0, 'travel_impact': 0.8, 'duration': 1, 'name': 'Gandhi Jayanti'},
            '2023-10-24': {'type': 'religious', 'importance': 0.9, 'travel_impact': 0.9, 'duration': 2, 'name': 'Dussehra'},
            '2023-11-13': {'type': 'religious', 'importance': 1.0, 'travel_impact': 1.0, 'duration': 5, 'name': 'Diwali'},
            '2023-12-25': {'type': 'religious', 'importance': 0.8, 'travel_impact': 0.7, 'duration': 1, 'name': 'Christmas'},
            
            # 2024
            '2024-01-26': {'type': 'national', 'importance': 1.0, 'travel_impact': 0.9, 'duration': 1, 'name': 'Republic Day'},
            '2024-03-25': {'type': 'religious', 'importance': 0.9, 'travel_impact': 0.8, 'duration': 2, 'name': 'Holi'},
            '2024-04-17': {'type': 'religious', 'importance': 0.8, 'travel_impact': 0.7, 'duration': 1, 'name': 'Ram Navami'},
            '2024-03-29': {'type': 'religious', 'importance': 0.7, 'travel_impact': 0.6, 'duration': 1, 'name': 'Good Friday'},
            '2024-05-01': {'type': 'national', 'importance': 0.6, 'travel_impact': 0.5, 'duration': 1, 'name': 'Labour Day'},
            '2024-08-15': {'type': 'national', 'importance': 1.0, 'travel_impact': 0.9, 'duration': 1, 'name': 'Independence Day'},
            '2024-09-07': {'type': 'religious', 'importance': 0.9, 'travel_impact': 0.8, 'duration': 3, 'name': 'Ganesh Chaturthi'},
            '2024-10-02': {'type': 'national', 'importance': 1.0, 'travel_impact': 0.8, 'duration': 1, 'name': 'Gandhi Jayanti'},
            '2024-10-12': {'type': 'religious', 'importance': 0.9, 'travel_impact': 0.9, 'duration': 2, 'name': 'Dussehra'},
            '2024-11-01': {'type': 'religious', 'importance': 1.0, 'travel_impact': 1.0, 'duration': 5, 'name': 'Diwali'},
            '2024-12-25': {'type': 'religious', 'importance': 0.8, 'travel_impact': 0.7, 'duration': 1, 'name': 'Christmas'},
            
            # 2025
            '2025-01-26': {'type': 'national', 'importance': 1.0, 'travel_impact': 0.9, 'duration': 1, 'name': 'Republic Day'},
            '2025-03-14': {'type': 'religious', 'importance': 0.9, 'travel_impact': 0.8, 'duration': 2, 'name': 'Holi'},
            '2025-04-06': {'type': 'religious', 'importance': 0.8, 'travel_impact': 0.7, 'duration': 1, 'name': 'Ram Navami'},
            '2025-04-18': {'type': 'religious', 'importance': 0.7, 'travel_impact': 0.6, 'duration': 1, 'name': 'Good Friday'},
            '2025-05-01': {'type': 'national', 'importance': 0.6, 'travel_impact': 0.5, 'duration': 1, 'name': 'Labour Day'},
            '2025-08-15': {'type': 'national', 'importance': 1.0, 'travel_impact': 0.9, 'duration': 1, 'name': 'Independence Day'},
            '2025-08-27': {'type': 'religious', 'importance': 0.9, 'travel_impact': 0.8, 'duration': 3, 'name': 'Ganesh Chaturthi'},
            '2025-10-02': {'type': 'national', 'importance': 1.0, 'travel_impact': 0.8, 'duration': 1, 'name': 'Gandhi Jayanti'},
            '2025-10-20': {'type': 'religious', 'importance': 1.0, 'travel_impact': 1.0, 'duration': 5, 'name': 'Diwali'},
            '2025-12-25': {'type': 'religious', 'importance': 0.8, 'travel_impact': 0.7, 'duration': 1, 'name': 'Christmas'},
        }
        
        self.holidays = pd.to_datetime(list(self.holiday_data.keys()))
        
        # Advanced festival seasons with overlapping periods
        self.festival_seasons = {
            'diwali_mega_season': [('2023-10-10', '2023-11-25'), ('2024-10-10', '2024-11-15'), ('2025-10-10', '2025-10-30')],
            'holi_season': [('2023-03-01', '2023-03-20'), ('2024-03-15', '2024-04-05'), ('2025-03-05', '2025-03-25')],
            'summer_vacation_peak': [('2023-04-10', '2023-06-20'), ('2024-04-10', '2024-06-20'), ('2025-04-10', '2025-06-20')],
            'winter_vacation_peak': [('2023-12-10', '2024-01-20'), ('2024-12-10', '2025-01-20')],
            'monsoon_low_season': [('2023-07-01', '2023-09-15'), ('2024-07-01', '2024-09-15'), ('2025-07-01', '2025-09-15')],
            'wedding_season': [('2023-11-15', '2024-02-15'), ('2024-11-15', '2025-02-15')],
            'exam_season': [('2023-03-01', '2023-05-31'), ('2024-03-01', '2024-05-31'), ('2025-03-01', '2025-05-31')],
            'corporate_travel_peak': [('2023-01-15', '2023-03-31'), ('2023-09-01', '2023-11-30'), 
                                    ('2024-01-15', '2024-03-31'), ('2024-09-01', '2024-11-30'),
                                    ('2025-01-15', '2025-03-31')]
        }
        
        return self

    def _days_to_holiday(self, date):
        """Calculate days to nearest holiday"""
        if len(self.holidays) == 0:
            return 365
        days_diff = np.abs((self.holidays - date).days)
        return days_diff.min()

    def _days_from_holiday(self, date):
        """Calculate days from nearest past holiday"""
        past_holidays = self.holidays[self.holidays <= date]
        if len(past_holidays) == 0:
            return 365
        return (date - past_holidays.max()).days

    def _get_holiday_importance(self, date):
        """Get holiday importance with distance decay"""
        date_str = date.strftime('%Y-%m-%d')
        if date_str in self.holiday_data:
            return self.holiday_data[date_str]['importance']

        if len(self.holidays) == 0:
            return 0

        nearest_holiday = self.holidays[np.argmin(np.abs((self.holidays - date).days))]
        nearest_str = nearest_holiday.strftime('%Y-%m-%d')

        if nearest_str in self.holiday_data:
            distance = abs((nearest_holiday - date).days)
            decay_factor = max(0, 1 - distance / 21)  # 21-day decay
            return self.holiday_data[nearest_str]['importance'] * decay_factor

        return 0

    def _get_holiday_travel_impact(self, date):
        """Get holiday travel impact"""
        date_str = date.strftime('%Y-%m-%d')
        if date_str in self.holiday_data:
            return self.holiday_data[date_str]['travel_impact']

        if len(self.holidays) == 0:
            return 0

        nearest_holiday = self.holidays[np.argmin(np.abs((self.holidays - date).days))]
        nearest_str = nearest_holiday.strftime('%Y-%m-%d')

        if nearest_str in self.holiday_data:
            distance = abs((nearest_holiday - date).days)
            decay_factor = max(0, 1 - distance / 14)
            return self.holiday_data[nearest_str]['travel_impact'] * decay_factor

        return 0

    def _get_holiday_duration(self, date):
        """Get holiday duration"""
        date_str = date.strftime('%Y-%m-%d')
        if date_str in self.holiday_data:
            return self.holiday_data[date_str]['duration']
        return 0

    def _is_holiday_type(self, date, holiday_type):
        """Check if date is a specific type of holiday"""
        date_str = date.strftime('%Y-%m-%d')
        if date_str in self.holiday_data:
            return self.holiday_data[date_str]['type'] == holiday_type
        return False

    def engineer_elite_features(self, df, is_train=True):
        """🚀 Engineer 200+ ELITE features for TOP-50 breakthrough"""
        print(f"🔧 Engineering ELITE features for {'training' if is_train else 'test'} data...")

        df = df.copy()
        df['doj'] = pd.to_datetime(df['doj'])

        # === CORE TEMPORAL FEATURES ===
        df['year'] = df['doj'].dt.year
        df['month'] = df['doj'].dt.month
        df['day'] = df['doj'].dt.day
        df['dayofweek'] = df['doj'].dt.dayofweek
        df['dayofyear'] = df['doj'].dt.dayofyear
        df['week'] = df['doj'].dt.isocalendar().week
        df['quarter'] = df['doj'].dt.quarter

        # === ADVANCED TEMPORAL FEATURES ===
        df['is_weekend'] = (df['dayofweek'] >= 5).astype(int)
        df['is_monday'] = (df['dayofweek'] == 0).astype(int)
        df['is_friday'] = (df['dayofweek'] == 4).astype(int)
        df['is_month_start'] = (df['day'] <= 5).astype(int)
        df['is_month_end'] = (df['day'] >= 25).astype(int)
        df['is_quarter_start'] = ((df['day'] <= 5) & (df['month'].isin([1, 4, 7, 10]))).astype(int)
        df['is_quarter_end'] = ((df['day'] >= 25) & (df['month'].isin([3, 6, 9, 12]))).astype(int)

        # === FOURIER ANALYSIS & CYCLICAL FEATURES ===
        periods = [7, 14, 30.44, 91.31, 365.25]
        for period in periods:
            df[f'sin_{period:.0f}'] = np.sin(2 * np.pi * df['dayofyear'] / period)
            df[f'cos_{period:.0f}'] = np.cos(2 * np.pi * df['dayofyear'] / period)

        df['dow_sin'] = np.sin(2 * np.pi * df['dayofweek'] / 7)
        df['dow_cos'] = np.cos(2 * np.pi * df['dayofweek'] / 7)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)

        # === ELITE HOLIDAY FEATURES ===
        df['days_to_holiday'] = df['doj'].apply(self._days_to_holiday)
        df['days_from_holiday'] = df['doj'].apply(self._days_from_holiday)
        df['holiday_importance'] = df['doj'].apply(self._get_holiday_importance)
        df['holiday_travel_impact'] = df['doj'].apply(self._get_holiday_travel_impact)
        df['holiday_duration'] = df['doj'].apply(self._get_holiday_duration)

        df['is_holiday'] = df['doj'].isin(self.holidays).astype(int)
        df['is_pre_holiday'] = df['doj'].apply(lambda x: (x + timedelta(days=1)) in self.holidays.values).astype(int)
        df['is_post_holiday'] = df['doj'].apply(lambda x: (x - timedelta(days=1)) in self.holidays.values).astype(int)
        df['is_holiday_week'] = df['doj'].apply(lambda x: any(abs((x - h).days) <= 7 for h in self.holidays)).astype(int)

        # Holiday proximity with exponential decay
        for days in [1, 2, 3, 5, 7, 14]:
            df[f'holiday_within_{days}d'] = df['doj'].apply(
                lambda x: any(abs((x - h).days) <= days for h in self.holidays)
            ).astype(int)

        # Holiday type features
        df['is_national_holiday'] = df['doj'].apply(self._is_holiday_type, args=('national',)).astype(int)
        df['is_religious_holiday'] = df['doj'].apply(self._is_holiday_type, args=('religious',)).astype(int)

        # Festival seasons
        for season, periods in self.festival_seasons.items():
            df[f'is_{season}'] = 0
            for start_str, end_str in periods:
                start_date = pd.to_datetime(start_str)
                end_date = pd.to_datetime(end_str)
                mask = (df['doj'] >= start_date) & (df['doj'] <= end_date)
                df.loc[mask, f'is_{season}'] = 1

        # === ROUTE INTELLIGENCE ===
        df['route'] = df['srcid'].astype(str) + '_' + df['destid'].astype(str)
        df['route_distance'] = np.sqrt((df['srcid'] - df['destid'])**2)
        df['route_sum'] = df['srcid'] + df['destid']
        df['route_diff'] = np.abs(df['srcid'] - df['destid'])
        df['route_product'] = df['srcid'] * df['destid']
        df['route_ratio'] = df['srcid'] / (df['destid'] + 1)

        # === SEASONAL & BUSINESS FEATURES ===
        df['season'] = df['month'].map({12: 0, 1: 0, 2: 0, 3: 1, 4: 1, 5: 1, 6: 2, 7: 2, 8: 2, 9: 3, 10: 3, 11: 3})
        df['is_summer'] = (df['season'] == 2).astype(int)
        df['is_winter'] = (df['season'] == 0).astype(int)
        df['is_monsoon'] = df['month'].isin([6, 7, 8, 9]).astype(int)
        df['is_peak_travel'] = df['month'].isin([4, 5, 10, 11, 12]).astype(int)

        df['is_business_day'] = ((df['dayofweek'] < 5) & (~df['is_holiday'])).astype(int)
        df['is_leisure_day'] = (df['is_weekend'] | df['is_holiday']).astype(int)
        df['is_long_weekend'] = ((df['dayofweek'] == 4) & df['is_pre_holiday']) | ((df['dayofweek'] == 0) & df['is_post_holiday'])
        df['is_long_weekend'] = df['is_long_weekend'].astype(int)

        # Economic indicators
        df['is_salary_day'] = (df['day'] == 1).astype(int)
        df['is_month_end_salary'] = (df['day'] >= 28).astype(int)
        df['is_mid_month'] = ((df['day'] >= 10) & (df['day'] <= 20)).astype(int)

        if is_train:
            self._calculate_elite_route_stats(df)
            self._create_elite_clusters(df)

        df = self._add_elite_route_features(df)

        return df

    def _calculate_elite_route_stats(self, df):
        """Calculate elite route statistics"""
        print("📊 Calculating elite route statistics...")

        # Basic statistics with advanced metrics
        route_stats = df.groupby('route')['final_seatcount'].agg([
            'mean', 'std', 'median', 'min', 'max', 'count', 'skew', 'sum'
        ]).reset_index()
        route_stats.columns = ['route', 'route_mean', 'route_std', 'route_median',
                              'route_min', 'route_max', 'route_count', 'route_skew', 'route_sum']

        # Advanced percentiles
        percentiles = df.groupby('route')['final_seatcount'].quantile([0.1, 0.25, 0.75, 0.9]).unstack()
        percentiles.columns = ['route_p10', 'route_p25', 'route_p75', 'route_p90']
        percentiles['route_iqr'] = percentiles['route_p75'] - percentiles['route_p25']
        percentiles['route_range'] = percentiles['route_p90'] - percentiles['route_p10']
        percentiles = percentiles.reset_index()

        # Time-based patterns with multiple dimensions
        dow_stats = df.groupby(['route', 'dayofweek'])['final_seatcount'].agg(['mean', 'std', 'count']).reset_index()
        dow_pivot = dow_stats.pivot(index='route', columns='dayofweek', values=['mean', 'std', 'count'])
        dow_pivot.columns = [f'route_dow_{int(col[1])}_{col[0]}' for col in dow_pivot.columns]
        dow_pivot = dow_pivot.reset_index()

        # Month patterns
        month_stats = df.groupby(['route', 'month'])['final_seatcount'].agg(['mean', 'std']).reset_index()
        month_pivot = month_stats.pivot(index='route', columns='month', values=['mean', 'std'])
        month_pivot.columns = [f'route_month_{int(col[1])}_{col[0]}' for col in month_pivot.columns]
        month_pivot = month_pivot.reset_index()

        # Holiday patterns
        holiday_stats = df.groupby(['route', 'is_holiday'])['final_seatcount'].agg(['mean', 'std', 'count']).reset_index()
        holiday_pivot = holiday_stats.pivot(index='route', columns='is_holiday', values=['mean', 'std', 'count'])
        holiday_pivot.columns = [f'route_holiday_{int(col[1])}_{col[0]}' for col in holiday_pivot.columns]
        holiday_pivot = holiday_pivot.reset_index()

        # Weekend patterns
        df['is_weekend_temp'] = df['is_weekend']
        weekend_stats = df.groupby(['route', 'is_weekend_temp'])['final_seatcount'].agg(['mean', 'std']).reset_index()
        weekend_pivot = weekend_stats.pivot(index='route', columns='is_weekend_temp', values=['mean', 'std'])
        weekend_pivot.columns = [f'route_weekend_{int(col[1])}_{col[0]}' for col in weekend_pivot.columns]
        weekend_pivot = weekend_pivot.reset_index()

        # Merge all statistics
        self.route_stats = route_stats
        for stats_df in [percentiles, dow_pivot, month_pivot, holiday_pivot, weekend_pivot]:
            self.route_stats = self.route_stats.merge(stats_df, on='route', how='left')

        # Fill missing values with sophisticated imputation
        for col in self.route_stats.columns:
            if col != 'route':
                self.route_stats[col] = self.route_stats[col].fillna(self.route_stats[col].median())

    def _create_elite_clusters(self, df):
        """Create elite route clusters"""
        print("🎯 Creating elite route clusters...")

        # Prepare comprehensive features for clustering
        route_features = df.groupby('route')['final_seatcount'].agg([
            'mean', 'std', 'median', 'min', 'max', 'count', 'skew'
        ]).fillna(0)

        # Add temporal patterns
        dow_patterns = df.groupby(['route', 'dayofweek'])['final_seatcount'].mean().unstack(fill_value=0)
        dow_patterns.columns = [f'dow_{col}' for col in dow_patterns.columns]

        month_patterns = df.groupby(['route', 'month'])['final_seatcount'].mean().unstack(fill_value=0)
        month_patterns.columns = [f'month_{col}' for col in month_patterns.columns]

        # Combine features
        cluster_features = pd.concat([route_features, dow_patterns, month_patterns], axis=1).fillna(0)

        if len(cluster_features) > 10:
            # Multiple clustering algorithms
            scaler = StandardScaler()
            scaled_features = scaler.fit_transform(cluster_features)

            # K-means clustering
            n_clusters_kmeans = min(20, max(3, len(cluster_features) // 6))
            kmeans = KMeans(n_clusters=n_clusters_kmeans, random_state=42, n_init=10)
            kmeans_clusters = kmeans.fit_predict(scaled_features)

            # DBSCAN for density-based clustering
            dbscan = DBSCAN(eps=0.5, min_samples=3)
            dbscan_clusters = dbscan.fit_predict(scaled_features)

            # Agglomerative clustering
            n_clusters_agg = min(15, max(3, len(cluster_features) // 8))
            agg_clustering = AgglomerativeClustering(n_clusters=n_clusters_agg)
            agg_clusters = agg_clustering.fit_predict(scaled_features)

            # Store multiple cluster assignments
            self.route_clusters = {
                'kmeans': dict(zip(cluster_features.index, kmeans_clusters)),
                'dbscan': dict(zip(cluster_features.index, dbscan_clusters)),
                'agglomerative': dict(zip(cluster_features.index, agg_clusters))
            }
        else:
            # Default clustering for small datasets
            self.route_clusters = {
                'kmeans': {route: 0 for route in cluster_features.index},
                'dbscan': {route: 0 for route in cluster_features.index},
                'agglomerative': {route: 0 for route in cluster_features.index}
            }

    def _add_elite_route_features(self, df):
        """Add elite route features"""
        # Add route statistics
        df = df.merge(self.route_stats, on='route', how='left')

        # Add multiple cluster information
        df['route_cluster_kmeans'] = df['route'].map(self.route_clusters.get('kmeans', {})).fillna(0)
        df['route_cluster_dbscan'] = df['route'].map(self.route_clusters.get('dbscan', {})).fillna(-1)
        df['route_cluster_agg'] = df['route'].map(self.route_clusters.get('agglomerative', {})).fillna(0)

        # Advanced route features
        if 'route_count' in df.columns:
            df['route_popularity'] = pd.cut(df['route_count'], bins=5, labels=[0,1,2,3,4], duplicates='drop').astype(float)

        if 'route_std' in df.columns and 'route_mean' in df.columns:
            df['route_volatility'] = df['route_std'] / (df['route_mean'] + 1)
            df['route_cv'] = df['route_std'] / (df['route_mean'] + 1)

        # Fill missing values
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            if col not in ['srcid', 'destid', 'year', 'month', 'day']:
                df[col] = df[col].fillna(df[col].median())

        return df
