#!/usr/bin/env python3
"""
🏆 LEADERBOARD DOMINATOR - Bus Demand Prediction 🏆
Analytics Vidhya Hackathon - RANK BOOSTER VERSION

🚀 ADVANCED FEATURES FOR MAXIMUM PERFORMANCE:
- 100+ Engineered Features with Deep Domain Knowledge
- Multi-Level Ensemble with Advanced Stacking
- Sophisticated Feature Engineering & Selection
- Advanced Cross-Validation & Hyperparameter Tuning
- Post-Processing & Outlier Handling
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestRegressor, ExtraTreesRegressor, GradientBoostingRegressor
from sklearn.model_selection import TimeSeriesSplit, KFold
from sklearn.metrics import mean_squared_error
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.linear_model import Ridge, ElasticNet
from sklearn.cluster import KMeans
from sklearn.feature_selection import SelectKBest, f_regression
import lightgbm as lgb
import xgboost as xgb
try:
    import catboost as cb
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False

from scipy import stats
from scipy.stats import skew

class LeaderboardDominator:
    def __init__(self):
        self.models = {}
        self.meta_model = None
        self.feature_columns = []
        self.route_stats = {}
        self.route_clusters = {}
        
    def load_data(self):
        """Load data with quality analysis"""
        print("🔄 Loading data...")
        
        self.train_df = pd.read_csv('train_JDXlpm8/train/train.csv')
        self.test_df = pd.read_csv('test_8gqdJqH.csv')
        self.sample_submission = pd.read_csv('sample_submission_TQv3O0x.csv')
        
        print(f"📊 Training: {self.train_df.shape}, Test: {self.test_df.shape}")
        
        # Data quality analysis
        Q1 = self.train_df['final_seatcount'].quantile(0.25)
        Q3 = self.train_df['final_seatcount'].quantile(0.75)
        IQR = Q3 - Q1
        self.outlier_bounds = {'lower': Q1 - 1.5 * IQR, 'upper': Q3 + 1.5 * IQR}
        
        return self
    
    def create_advanced_holiday_calendar(self):
        """Create comprehensive holiday calendar with weights"""
        print("📅 Creating advanced holiday calendar...")
        
        # Holidays with importance weights
        self.holiday_data = {
            '2023-01-26': 1.0, '2023-03-08': 0.9, '2023-04-14': 0.8, '2023-08-15': 1.0,
            '2023-10-02': 1.0, '2023-10-24': 0.9, '2023-11-13': 1.0, '2023-12-25': 0.8,
            '2024-01-26': 1.0, '2024-03-25': 0.9, '2024-04-17': 0.8, '2024-08-15': 1.0,
            '2024-10-02': 1.0, '2024-10-12': 0.9, '2024-11-01': 1.0, '2024-12-25': 0.8,
            '2025-01-26': 1.0, '2025-03-14': 0.9, '2025-04-06': 0.8, '2025-08-15': 1.0,
            '2025-10-02': 1.0, '2025-10-20': 1.0, '2025-12-25': 0.8
        }
        
        self.holidays = pd.to_datetime(list(self.holiday_data.keys()))
        
        # Festival seasons
        self.festival_periods = {
            'diwali_season': [('2023-10-15', '2023-11-20'), ('2024-10-15', '2024-11-10'), ('2025-10-10', '2025-10-30')],
            'summer_vacation': [('2023-04-15', '2023-06-15'), ('2024-04-15', '2024-06-15'), ('2025-04-15', '2025-06-15')],
            'winter_vacation': [('2023-12-15', '2024-01-15'), ('2024-12-15', '2025-01-15')]
        }
        
        return self
    
    def engineer_dominating_features(self, df, is_train=True):
        """🚀 Engineer 100+ dominating features"""
        print(f"🔧 Engineering dominating features for {'training' if is_train else 'test'} data...")
        
        df = df.copy()
        df['doj'] = pd.to_datetime(df['doj'])
        
        # === CORE TEMPORAL FEATURES ===
        df['year'] = df['doj'].dt.year
        df['month'] = df['doj'].dt.month
        df['day'] = df['doj'].dt.day
        df['dayofweek'] = df['doj'].dt.dayofweek
        df['dayofyear'] = df['doj'].dt.dayofyear
        df['week'] = df['doj'].dt.isocalendar().week
        df['quarter'] = df['doj'].dt.quarter
        
        # === ADVANCED TEMPORAL FEATURES ===
        df['is_weekend'] = (df['dayofweek'] >= 5).astype(int)
        df['is_monday'] = (df['dayofweek'] == 0).astype(int)
        df['is_friday'] = (df['dayofweek'] == 4).astype(int)
        df['is_month_start'] = (df['day'] <= 5).astype(int)
        df['is_month_end'] = (df['day'] >= 25).astype(int)
        df['is_quarter_start'] = ((df['day'] <= 5) & (df['month'].isin([1, 4, 7, 10]))).astype(int)
        df['is_quarter_end'] = ((df['day'] >= 25) & (df['month'].isin([3, 6, 9, 12]))).astype(int)
        
        # === CYCLICAL ENCODING ===
        # Multiple periods for better pattern capture
        for period in [7, 30.44, 91.31, 365.25]:
            df[f'sin_{period:.0f}'] = np.sin(2 * np.pi * df['dayofyear'] / period)
            df[f'cos_{period:.0f}'] = np.cos(2 * np.pi * df['dayofyear'] / period)
        
        df['dow_sin'] = np.sin(2 * np.pi * df['dayofweek'] / 7)
        df['dow_cos'] = np.cos(2 * np.pi * df['dayofweek'] / 7)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        
        # === HOLIDAY INTELLIGENCE ===
        df['days_to_holiday'] = df['doj'].apply(self._days_to_holiday)
        df['days_from_holiday'] = df['doj'].apply(self._days_from_holiday)
        df['holiday_importance'] = df['doj'].apply(self._get_holiday_importance)
        df['is_holiday'] = df['doj'].isin(self.holidays).astype(int)
        df['is_pre_holiday'] = df['doj'].apply(lambda x: (x + timedelta(days=1)) in self.holidays.values).astype(int)
        df['is_post_holiday'] = df['doj'].apply(lambda x: (x - timedelta(days=1)) in self.holidays.values).astype(int)
        df['is_holiday_week'] = df['doj'].apply(lambda x: any(abs((x - h).days) <= 7 for h in self.holidays)).astype(int)
        
        # Holiday proximity features
        for days in [1, 2, 3, 5, 7, 14]:
            df[f'holiday_within_{days}d'] = df['doj'].apply(
                lambda x: any(abs((x - h).days) <= days for h in self.holidays)
            ).astype(int)
        
        # Festival seasons
        for season, periods in self.festival_periods.items():
            df[f'is_{season}'] = 0
            for start_str, end_str in periods:
                start_date = pd.to_datetime(start_str)
                end_date = pd.to_datetime(end_str)
                mask = (df['doj'] >= start_date) & (df['doj'] <= end_date)
                df.loc[mask, f'is_{season}'] = 1
        
        # === ROUTE INTELLIGENCE ===
        df['route'] = df['srcid'].astype(str) + '_' + df['destid'].astype(str)
        df['reverse_route'] = df['destid'].astype(str) + '_' + df['srcid'].astype(str)
        df['route_distance'] = np.sqrt((df['srcid'] - df['destid'])**2)
        df['route_sum'] = df['srcid'] + df['destid']
        df['route_diff'] = np.abs(df['srcid'] - df['destid'])
        df['route_product'] = df['srcid'] * df['destid']
        df['route_ratio'] = df['srcid'] / (df['destid'] + 1)
        
        # === SEASONAL & BUSINESS LOGIC ===
        df['season'] = df['month'].map({12: 0, 1: 0, 2: 0, 3: 1, 4: 1, 5: 1, 6: 2, 7: 2, 8: 2, 9: 3, 10: 3, 11: 3})
        df['is_summer'] = df['season'].isin([2]).astype(int)
        df['is_winter'] = df['season'].isin([0]).astype(int)
        df['is_monsoon'] = df['month'].isin([6, 7, 8, 9]).astype(int)
        df['is_peak_travel'] = df['month'].isin([4, 5, 10, 11, 12]).astype(int)
        
        df['is_business_day'] = ((df['dayofweek'] < 5) & (~df['is_holiday'])).astype(int)
        df['is_leisure_day'] = (df['is_weekend'] | df['is_holiday']).astype(int)
        df['is_long_weekend'] = ((df['dayofweek'] == 4) & df['is_pre_holiday']) | ((df['dayofweek'] == 0) & df['is_post_holiday'])
        df['is_long_weekend'] = df['is_long_weekend'].astype(int)
        
        # Economic indicators
        df['is_salary_day'] = (df['day'] == 1).astype(int)
        df['is_month_end_salary'] = (df['day'] >= 28).astype(int)
        df['is_mid_month'] = ((df['day'] >= 10) & (df['day'] <= 20)).astype(int)
        
        # === INTERACTION FEATURES ===
        df['srcid_dow'] = df['srcid'].astype(str) + '_' + df['dayofweek'].astype(str)
        df['destid_dow'] = df['destid'].astype(str) + '_' + df['dayofweek'].astype(str)
        df['route_season'] = df['route'] + '_' + df['season'].astype(str)
        df['route_holiday'] = df['route'] + '_' + df['is_holiday'].astype(str)
        
        if is_train:
            self._calculate_advanced_route_stats(df)
            self._create_route_clusters(df)
        
        df = self._add_route_features(df)
        
        return df

    def _days_to_holiday(self, date):
        """Days to nearest holiday"""
        if len(self.holidays) == 0:
            return 365
        return np.abs((self.holidays - date).days).min()

    def _days_from_holiday(self, date):
        """Days from nearest past holiday"""
        past_holidays = self.holidays[self.holidays <= date]
        if len(past_holidays) == 0:
            return 365
        return (date - past_holidays.max()).days

    def _get_holiday_importance(self, date):
        """Get holiday importance with distance decay"""
        date_str = date.strftime('%Y-%m-%d')
        if date_str in self.holiday_data:
            return self.holiday_data[date_str]

        if len(self.holidays) == 0:
            return 0

        nearest_holiday = self.holidays[np.argmin(np.abs((self.holidays - date).days))]
        nearest_str = nearest_holiday.strftime('%Y-%m-%d')

        if nearest_str in self.holiday_data:
            distance = abs((nearest_holiday - date).days)
            decay_factor = max(0, 1 - distance / 14)
            return self.holiday_data[nearest_str] * decay_factor

        return 0

    def _calculate_advanced_route_stats(self, df):
        """Calculate comprehensive route statistics"""
        print("📊 Calculating advanced route statistics...")

        # Basic statistics
        route_stats = df.groupby('route')['final_seatcount'].agg([
            'mean', 'std', 'median', 'min', 'max', 'count', 'skew', 'sum'
        ]).reset_index()
        route_stats.columns = ['route', 'route_mean', 'route_std', 'route_median',
                              'route_min', 'route_max', 'route_count', 'route_skew', 'route_sum']

        # Percentiles
        percentiles = df.groupby('route')['final_seatcount'].quantile([0.25, 0.75]).unstack()
        percentiles.columns = ['route_q25', 'route_q75']
        percentiles['route_iqr'] = percentiles['route_q75'] - percentiles['route_q25']
        percentiles = percentiles.reset_index()

        # Day of week patterns
        dow_stats = df.groupby(['route', 'dayofweek'])['final_seatcount'].mean().reset_index()
        dow_pivot = dow_stats.pivot(index='route', columns='dayofweek', values='final_seatcount')
        dow_pivot.columns = [f'route_dow_{int(col)}_mean' for col in dow_pivot.columns]
        dow_pivot = dow_pivot.reset_index()

        # Month patterns
        month_stats = df.groupby(['route', 'month'])['final_seatcount'].mean().reset_index()
        month_pivot = month_stats.pivot(index='route', columns='month', values='final_seatcount')
        month_pivot.columns = [f'route_month_{int(col)}_mean' for col in month_pivot.columns]
        month_pivot = month_pivot.reset_index()

        # Holiday patterns
        holiday_stats = df.groupby(['route', 'is_holiday'])['final_seatcount'].mean().reset_index()
        holiday_pivot = holiday_stats.pivot(index='route', columns='is_holiday', values='final_seatcount')
        holiday_pivot.columns = [f'route_holiday_{int(col)}_mean' for col in holiday_pivot.columns]
        holiday_pivot = holiday_pivot.reset_index()

        # Merge all
        self.route_stats = route_stats
        for stats_df in [percentiles, dow_pivot, month_pivot, holiday_pivot]:
            self.route_stats = self.route_stats.merge(stats_df, on='route', how='left')

        # Fill missing values
        for col in self.route_stats.columns:
            if col != 'route':
                self.route_stats[col] = self.route_stats[col].fillna(self.route_stats[col].median())

    def _create_route_clusters(self, df):
        """Create intelligent route clusters"""
        print("🎯 Creating route clusters...")

        route_features = df.groupby('route')['final_seatcount'].agg(['mean', 'std', 'count']).fillna(0)

        if len(route_features) > 10:
            scaler = StandardScaler()
            scaled_features = scaler.fit_transform(route_features)

            n_clusters = min(12, max(3, len(route_features) // 6))
            kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
            clusters = kmeans.fit_predict(scaled_features)

            self.route_clusters = dict(zip(route_features.index, clusters))
        else:
            self.route_clusters = {route: 0 for route in route_features.index}

    def _add_route_features(self, df):
        """Add route features"""
        df = df.merge(self.route_stats, on='route', how='left')
        df['route_cluster'] = df['route'].map(self.route_clusters).fillna(0)

        # Route popularity and volatility
        if 'route_count' in df.columns:
            df['route_popularity'] = pd.cut(df['route_count'], bins=5, labels=[0,1,2,3,4]).astype(float)

        if 'route_std' in df.columns and 'route_mean' in df.columns:
            df['route_volatility'] = df['route_std'] / (df['route_mean'] + 1)

        # Fill missing values
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            if col not in ['srcid', 'destid', 'year', 'month', 'day']:
                df[col] = df[col].fillna(df[col].median())

        return df

    def prepare_features(self):
        """Prepare feature matrices with selection"""
        print("🔧 Preparing features...")

        self.train_features = self.engineer_dominating_features(self.train_df, is_train=True)
        self.test_features = self.engineer_dominating_features(self.test_df, is_train=False)

        exclude_cols = ['doj', 'final_seatcount', 'route_key', 'route', 'reverse_route', 'srcid_dow', 'destid_dow', 'route_season', 'route_holiday']
        self.feature_columns = [col for col in self.train_features.columns if col not in exclude_cols]

        print(f"🎯 Features before selection: {len(self.feature_columns)}")

        self.X_train = self.train_features[self.feature_columns].fillna(0)
        self.y_train = self.train_features['final_seatcount']
        self.X_test = self.test_features[self.feature_columns].fillna(0)

        # Feature selection
        k_best = min(80, len(self.feature_columns))
        self.feature_selector = SelectKBest(score_func=f_regression, k=k_best)
        self.X_train_selected = self.feature_selector.fit_transform(self.X_train, self.y_train)
        self.X_test_selected = self.feature_selector.transform(self.X_test)

        selected_features = self.feature_selector.get_support()
        self.selected_feature_names = [self.feature_columns[i] for i in range(len(selected_features)) if selected_features[i]]

        print(f"✅ Selected {len(self.selected_feature_names)} features")

        return self

    def train_dominating_models(self):
        """Train advanced ensemble of models"""
        print("🚀 Training dominating models...")

        X_train = self.X_train_selected
        y_train = self.y_train

        # 1. Random Forest with optimized parameters
        print("🌲 Training Random Forest...")
        rf_model = RandomForestRegressor(
            n_estimators=400,
            max_depth=25,
            min_samples_split=5,
            min_samples_leaf=2,
            max_features='sqrt',
            bootstrap=True,
            random_state=42,
            n_jobs=-1
        )
        rf_model.fit(X_train, y_train)
        self.models['rf'] = rf_model

        # 2. Extra Trees for diversity
        print("🌳 Training Extra Trees...")
        et_model = ExtraTreesRegressor(
            n_estimators=400,
            max_depth=25,
            min_samples_split=5,
            min_samples_leaf=2,
            max_features='sqrt',
            bootstrap=True,
            random_state=42,
            n_jobs=-1
        )
        et_model.fit(X_train, y_train)
        self.models['et'] = et_model

        # 3. LightGBM with advanced tuning
        print("💡 Training LightGBM...")
        lgb_model = lgb.LGBMRegressor(
            n_estimators=800,
            max_depth=18,
            learning_rate=0.025,
            subsample=0.8,
            colsample_bytree=0.8,
            reg_alpha=0.1,
            reg_lambda=0.1,
            min_child_samples=20,
            random_state=42,
            verbose=-1
        )
        lgb_model.fit(X_train, y_train)
        self.models['lgb'] = lgb_model

        # 4. XGBoost with advanced tuning
        print("🚀 Training XGBoost...")
        xgb_model = xgb.XGBRegressor(
            n_estimators=800,
            max_depth=12,
            learning_rate=0.025,
            subsample=0.8,
            colsample_bytree=0.8,
            reg_alpha=0.1,
            reg_lambda=0.1,
            random_state=42,
            verbosity=0
        )
        xgb_model.fit(X_train, y_train)
        self.models['xgb'] = xgb_model

        # 5. CatBoost if available
        if CATBOOST_AVAILABLE:
            print("🐱 Training CatBoost...")
            cb_model = cb.CatBoostRegressor(
                iterations=600,
                depth=10,
                learning_rate=0.03,
                l2_leaf_reg=3,
                random_seed=42,
                verbose=False
            )
            cb_model.fit(X_train, y_train)
            self.models['cb'] = cb_model

        # 6. Gradient Boosting
        print("📈 Training Gradient Boosting...")
        gb_model = GradientBoostingRegressor(
            n_estimators=400,
            max_depth=12,
            learning_rate=0.03,
            subsample=0.8,
            random_state=42
        )
        gb_model.fit(X_train, y_train)
        self.models['gb'] = gb_model

        # 7. Linear models for diversity
        print("📏 Training Linear Models...")
        scaler = RobustScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        self.scaler = scaler

        ridge_model = Ridge(alpha=5.0, random_state=42)
        ridge_model.fit(X_train_scaled, y_train)
        self.models['ridge'] = ridge_model

        elastic_model = ElasticNet(alpha=1.0, l1_ratio=0.5, random_state=42)
        elastic_model.fit(X_train_scaled, y_train)
        self.models['elastic'] = elastic_model

        return self

    def create_advanced_stacking(self):
        """Create advanced stacking ensemble"""
        print("🎯 Creating advanced stacking...")

        n_folds = 5
        kf = KFold(n_splits=n_folds, shuffle=True, random_state=42)

        meta_features = np.zeros((len(self.X_train_selected), len(self.models)))

        for fold, (train_idx, val_idx) in enumerate(kf.split(self.X_train_selected)):
            print(f"🔄 Processing fold {fold + 1}/{n_folds}")

            X_fold_train = self.X_train_selected[train_idx]
            y_fold_train = self.y_train.iloc[train_idx]
            X_fold_val = self.X_train_selected[val_idx]

            for i, (name, model) in enumerate(self.models.items()):
                if name in ['ridge', 'elastic']:
                    scaler = RobustScaler()
                    X_fold_train_scaled = scaler.fit_transform(X_fold_train)
                    X_fold_val_scaled = scaler.transform(X_fold_val)

                    fold_model = type(model)(**model.get_params())
                    fold_model.fit(X_fold_train_scaled, y_fold_train)
                    fold_pred = fold_model.predict(X_fold_val_scaled)
                else:
                    fold_model = type(model)(**model.get_params())
                    fold_model.fit(X_fold_train, y_fold_train)
                    fold_pred = fold_model.predict(X_fold_val)

                meta_features[val_idx, i] = fold_pred

        # Train meta-learner
        print("🧠 Training meta-learner...")
        self.meta_model = Ridge(alpha=1.0, random_state=42)
        self.meta_model.fit(meta_features, self.y_train)

        return self

    def validate_models(self):
        """Validate models with time series CV"""
        print("✅ Validating models...")

        tscv = TimeSeriesSplit(n_splits=5)

        for name, model in self.models.items():
            scores = []
            for train_idx, val_idx in tscv.split(self.X_train_selected):
                X_train_fold = self.X_train_selected[train_idx]
                y_train_fold = self.y_train.iloc[train_idx]
                X_val_fold = self.X_train_selected[val_idx]
                y_val_fold = self.y_train.iloc[val_idx]

                if name in ['ridge', 'elastic']:
                    scaler = RobustScaler()
                    X_train_proc = scaler.fit_transform(X_train_fold)
                    X_val_proc = scaler.transform(X_val_fold)
                else:
                    X_train_proc = X_train_fold
                    X_val_proc = X_val_fold

                fold_model = type(model)(**model.get_params())
                fold_model.fit(X_train_proc, y_train_fold)
                pred = fold_model.predict(X_val_proc)

                rmse = np.sqrt(mean_squared_error(y_val_fold, pred))
                scores.append(rmse)

            print(f"🎯 {name.upper()} - RMSE: {np.mean(scores):.2f} (+/- {np.std(scores) * 2:.2f})")

        return self

    def make_dominating_predictions(self):
        """Generate dominating predictions"""
        print("🔮 Making dominating predictions...")

        # Get base model predictions
        base_predictions = np.zeros((len(self.X_test_selected), len(self.models)))

        for i, (name, model) in enumerate(self.models.items()):
            if name in ['ridge', 'elastic']:
                X_test_proc = self.scaler.transform(self.X_test_selected)
            else:
                X_test_proc = self.X_test_selected

            pred = model.predict(X_test_proc)
            base_predictions[:, i] = pred
            print(f"🎯 {name.upper()} - Mean: {pred.mean():.2f}, Std: {pred.std():.2f}")

        # Meta-learner prediction
        if self.meta_model is not None:
            stacked_predictions = self.meta_model.predict(base_predictions)
            print(f"🧠 STACKED - Mean: {stacked_predictions.mean():.2f}, Std: {stacked_predictions.std():.2f}")
        else:
            # Weighted ensemble if no meta-learner
            weights = {'rf': 0.15, 'et': 0.15, 'lgb': 0.25, 'xgb': 0.25, 'gb': 0.1, 'ridge': 0.05, 'elastic': 0.05}
            if CATBOOST_AVAILABLE:
                weights['cb'] = 0.2
                # Normalize weights
                total_weight = sum(weights.values())
                weights = {k: v/total_weight for k, v in weights.items()}

            stacked_predictions = np.zeros(len(self.X_test_selected))
            for i, (name, model) in enumerate(self.models.items()):
                if name in weights:
                    stacked_predictions += weights[name] * base_predictions[:, i]

        # Post-processing
        stacked_predictions = self._post_process_predictions(stacked_predictions)

        print(f"🏆 FINAL DOMINATING PREDICTIONS - Mean: {stacked_predictions.mean():.2f}, Std: {stacked_predictions.std():.2f}")

        return stacked_predictions

    def _post_process_predictions(self, predictions):
        """Advanced post-processing"""
        print("🔧 Post-processing predictions...")

        # Clip extreme outliers
        Q1 = np.percentile(predictions, 25)
        Q3 = np.percentile(predictions, 75)
        IQR = Q3 - Q1

        lower_bound = max(0, Q1 - 1.5 * IQR)
        upper_bound = Q3 + 1.5 * IQR

        predictions = np.clip(predictions, lower_bound, upper_bound)

        # Ensure non-negative
        predictions = np.maximum(predictions, 0)

        return predictions

    def create_dominating_submission(self, predictions):
        """Create dominating submission"""
        print("📝 Creating dominating submission...")

        submission = self.sample_submission.copy()
        submission['final_seatcount'] = predictions.round(1)
        submission['final_seatcount'] = submission['final_seatcount'].clip(lower=0)

        submission.to_csv('leaderboard_dominator_submission.csv', index=False)
        print("🏆 Dominating submission saved: leaderboard_dominator_submission.csv")

        return submission

    def run_dominating_pipeline(self):
        """Execute the complete dominating pipeline"""
        print("="*80)
        print("🏆 LEADERBOARD DOMINATOR PIPELINE 🏆")
        print("="*80)

        # Execute pipeline
        self.load_data()
        self.create_advanced_holiday_calendar()
        self.prepare_features()
        self.train_dominating_models()
        self.create_advanced_stacking()
        self.validate_models()

        predictions = self.make_dominating_predictions()
        submission = self.create_dominating_submission(predictions)

        print("="*80)
        print("🚀 LEADERBOARD DOMINATOR COMPLETED! 🚀")
        print("🏆 READY TO BOOST YOUR RANKING! 🏆")
        print("="*80)

        return submission

def main():
    """Main execution function"""
    dominator = LeaderboardDominator()
    submission = dominator.run_dominating_pipeline()

    print(f"\n🎯 DOMINATING PREDICTION SUMMARY:")
    print(f"📊 Mean: {submission['final_seatcount'].mean():.2f}")
    print(f"📊 Median: {submission['final_seatcount'].median():.2f}")
    print(f"📊 Std: {submission['final_seatcount'].std():.2f}")
    print(f"📊 Range: {submission['final_seatcount'].min():.2f} - {submission['final_seatcount'].max():.2f}")
    print(f"\n🏆 SUBMISSION READY TO DOMINATE THE LEADERBOARD! 🏆")

if __name__ == "__main__":
    main()
