#!/usr/bin/env python3
"""
🎯 SCORE BEATER SOLUTION 🎯
Bus Demand Prediction - BEAT 1120 SCORE

🔥 LASER FOCUSED ON PERFORMANCE
- Only proven features
- Optimized for your specific data patterns
- Target: Sub-1100 score
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestRegressor, ExtraTreesRegressor
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_squared_error
import lightgbm as lgb
import xgboost as xgb

class ScoreBeatSolution:
    def __init__(self):
        self.models = {}
        self.route_stats = {}
        
    def load_data(self):
        """Load data"""
        print("🔄 Loading data...")
        
        self.train_df = pd.read_csv('train_JDXlpm8/train/train.csv')
        self.test_df = pd.read_csv('test_8gqdJqH.csv')
        self.sample_submission = pd.read_csv('sample_submission_TQv3O0x.csv')
        
        print(f"📊 Training: {self.train_df.shape}, Test: {self.test_df.shape}")
        return self
    
    def create_holidays(self):
        """Essential holidays only"""
        holidays = [
            '2023-01-26', '2023-03-08', '2023-08-15', '2023-10-02', '2023-11-13', '2023-12-25',
            '2024-01-26', '2024-03-25', '2024-08-15', '2024-10-02', '2024-11-01', '2024-12-25',
            '2025-01-26', '2025-03-14', '2025-08-15', '2025-10-02', '2025-10-20', '2025-12-25'
        ]
        self.holidays = pd.to_datetime(holidays)
        return self
    
    def engineer_features(self, df, is_train=True):
        """Laser-focused feature engineering"""
        print(f"🔧 Engineering features for {'training' if is_train else 'test'} data...")
        
        df = df.copy()
        df['doj'] = pd.to_datetime(df['doj'])
        
        # Core temporal
        df['month'] = df['doj'].dt.month
        df['day'] = df['doj'].dt.day
        df['dayofweek'] = df['doj'].dt.dayofweek
        df['dayofyear'] = df['doj'].dt.dayofyear
        
        # Key cyclical patterns
        df['dow_sin'] = np.sin(2 * np.pi * df['dayofweek'] / 7)
        df['dow_cos'] = np.cos(2 * np.pi * df['dayofweek'] / 7)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        
        # Essential holiday features
        df['is_holiday'] = df['doj'].isin(self.holidays).astype(int)
        df['days_to_holiday'] = df['doj'].apply(lambda x: min([abs((x - h).days) for h in self.holidays] + [365]))
        df['holiday_within_7d'] = (df['days_to_holiday'] <= 7).astype(int)
        
        # Key temporal indicators
        df['is_weekend'] = (df['dayofweek'] >= 5).astype(int)
        df['is_month_end'] = (df['day'] >= 25).astype(int)
        df['is_friday'] = (df['dayofweek'] == 4).astype(int)
        
        # Route features
        df['route'] = df['srcid'].astype(str) + '_' + df['destid'].astype(str)
        df['route_distance'] = np.sqrt((df['srcid'] - df['destid'])**2)
        
        # Seasonal
        df['is_summer'] = df['month'].isin([4, 5, 6]).astype(int)
        df['is_winter'] = df['month'].isin([11, 12, 1, 2]).astype(int)
        
        if is_train:
            self._calculate_route_stats(df)
        
        df = self._add_route_features(df)
        
        return df
    
    def _calculate_route_stats(self, df):
        """Calculate essential route statistics"""
        print("📊 Calculating route statistics...")
        
        # Core route stats
        route_stats = df.groupby('route')['final_seatcount'].agg([
            'mean', 'std', 'count'
        ]).reset_index()
        route_stats.columns = ['route', 'route_mean', 'route_std', 'route_count']
        
        # Weekend vs weekday patterns
        weekend_stats = df.groupby(['route', 'is_weekend'])['final_seatcount'].mean().reset_index()
        weekend_pivot = weekend_stats.pivot(index='route', columns='is_weekend', values='final_seatcount')
        weekend_pivot.columns = [f'route_weekend_{int(col)}' for col in weekend_pivot.columns]
        weekend_pivot = weekend_pivot.reset_index()
        
        # Merge
        self.route_stats = route_stats.merge(weekend_pivot, on='route', how='left')
        
        # Fill missing
        for col in self.route_stats.columns:
            if col != 'route':
                self.route_stats[col] = self.route_stats[col].fillna(self.route_stats[col].median())
    
    def _add_route_features(self, df):
        """Add route features"""
        df = df.merge(self.route_stats, on='route', how='left')
        
        # Fill missing
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            if col not in ['srcid', 'destid']:
                df[col] = df[col].fillna(df[col].median())
        
        return df
    
    def prepare_features(self):
        """Prepare features"""
        print("🔧 Preparing features...")
        
        self.train_features = self.engineer_features(self.train_df, is_train=True)
        self.test_features = self.engineer_features(self.test_df, is_train=False)
        
        exclude_cols = ['doj', 'final_seatcount', 'route']
        self.feature_columns = [col for col in self.train_features.columns if col not in exclude_cols]
        
        print(f"🎯 Features: {len(self.feature_columns)}")
        
        self.X_train = self.train_features[self.feature_columns].fillna(0)
        self.y_train = self.train_features['final_seatcount']
        self.X_test = self.test_features[self.feature_columns].fillna(0)
        
        return self
    
    def train_models(self):
        """Train optimized models"""
        print("🚀 Training models...")
        
        # LightGBM - Primary model
        lgb_model = lgb.LGBMRegressor(
            n_estimators=400,
            max_depth=10,
            learning_rate=0.03,
            subsample=0.85,
            colsample_bytree=0.85,
            reg_alpha=0.05,
            reg_lambda=0.05,
            random_state=42,
            verbose=-1
        )
        lgb_model.fit(self.X_train, self.y_train)
        self.models['lgb'] = lgb_model
        
        # XGBoost - Secondary model
        xgb_model = xgb.XGBRegressor(
            n_estimators=400,
            max_depth=8,
            learning_rate=0.03,
            subsample=0.85,
            colsample_bytree=0.85,
            reg_alpha=0.05,
            reg_lambda=0.05,
            random_state=42,
            verbosity=0
        )
        xgb_model.fit(self.X_train, self.y_train)
        self.models['xgb'] = xgb_model
        
        # Random Forest - For stability
        rf_model = RandomForestRegressor(
            n_estimators=300,
            max_depth=20,
            min_samples_split=3,
            min_samples_leaf=1,
            random_state=42,
            n_jobs=-1
        )
        rf_model.fit(self.X_train, self.y_train)
        self.models['rf'] = rf_model
        
        # Extra Trees - For diversity
        et_model = ExtraTreesRegressor(
            n_estimators=300,
            max_depth=20,
            min_samples_split=3,
            min_samples_leaf=1,
            random_state=42,
            n_jobs=-1
        )
        et_model.fit(self.X_train, self.y_train)
        self.models['et'] = et_model
        
        return self
    
    def validate_models(self):
        """Validate models"""
        print("✅ Validating models...")
        
        tscv = TimeSeriesSplit(n_splits=3)
        
        for name, model in self.models.items():
            scores = []
            for train_idx, val_idx in tscv.split(self.X_train):
                X_train_fold = self.X_train.iloc[train_idx]
                y_train_fold = self.y_train.iloc[train_idx]
                X_val_fold = self.X_train.iloc[val_idx]
                y_val_fold = self.y_train.iloc[val_idx]
                
                fold_model = type(model)(**model.get_params())
                fold_model.fit(X_train_fold, y_train_fold)
                pred = fold_model.predict(X_val_fold)
                
                rmse = np.sqrt(mean_squared_error(y_val_fold, pred))
                scores.append(rmse)
            
            print(f"🎯 {name.upper()}: {np.mean(scores):.2f}")
        
        return self
    
    def make_predictions(self):
        """Make optimized predictions"""
        print("🔮 Making predictions...")
        
        # Get predictions
        predictions = {}
        for name, model in self.models.items():
            pred = model.predict(self.X_test)
            predictions[name] = pred
            print(f"🎯 {name}: {pred.mean():.2f}")
        
        # Optimized ensemble weights (based on validation performance)
        weights = {'lgb': 0.4, 'xgb': 0.35, 'rf': 0.15, 'et': 0.1}
        
        final_predictions = np.zeros(len(self.X_test))
        for name, weight in weights.items():
            final_predictions += weight * predictions[name]
        
        # Conservative post-processing
        final_predictions = np.clip(final_predictions, 0, 12000)
        
        print(f"🏆 FINAL: {final_predictions.mean():.2f}")
        
        return final_predictions
    
    def create_submission(self, predictions):
        """Create submission"""
        submission = self.sample_submission.copy()
        submission['final_seatcount'] = predictions.round(1)
        submission['final_seatcount'] = submission['final_seatcount'].clip(lower=0)
        
        submission.to_csv('score_beater_submission.csv', index=False)
        print("🏆 Submission saved: score_beater_submission.csv")
        
        return submission
    
    def run_pipeline(self):
        """Execute pipeline"""
        print("="*50)
        print("🎯 SCORE BEATER SOLUTION")
        print("🔥 TARGET: BEAT 1120 SCORE")
        print("="*50)
        
        self.load_data()
        self.create_holidays()
        self.prepare_features()
        self.train_models()
        self.validate_models()
        
        predictions = self.make_predictions()
        submission = self.create_submission(predictions)
        
        print("="*50)
        print("🚀 SCORE BEATER COMPLETED!")
        print("="*50)
        
        return submission

def main():
    solution = ScoreBeatSolution()
    submission = solution.run_pipeline()
    
    print(f"\n🎯 SCORE BEATER SUMMARY:")
    print(f"📊 Mean: {submission['final_seatcount'].mean():.2f}")
    print(f"📊 Median: {submission['final_seatcount'].median():.2f}")
    print(f"📊 Range: {submission['final_seatcount'].min():.2f} - {submission['final_seatcount'].max():.2f}")
    print(f"\n🏆 READY TO BEAT 1120! 🏆")

if __name__ == "__main__":
    main()
