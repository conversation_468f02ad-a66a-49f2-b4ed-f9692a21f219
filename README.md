# Bus Demand Prediction Solution

## Problem Overview

This solution addresses the Analytics Vidhya hackathon challenge to predict bus journey demand 15 days before the actual journey date. The goal is to forecast the total number of seats booked for each route using historical booking data.

## Key Challenge
- Predict demand influenced by holidays, wedding seasons, long weekends, school vacations, and exam schedules
- Handle route-level predictions with temporal patterns
- Achieve low RMSE (Root Mean Squared Error)

## Solution Approach

### 1. Feature Engineering
- **Temporal Features**: Day of week, month, quarter, season, day of year
- **Holiday Features**: Distance to nearest holiday, pre/post holiday indicators
- **Route Features**: Route-specific statistics (mean, std, median demand)
- **Calendar Features**: Weekend indicators, month start/end flags

### 2. Route-Specific Modeling
- Calculate historical statistics for each route
- Handle new routes with global averages
- Day-of-week and month-specific patterns per route

### 3. Ensemble Modeling
- **Random Forest**: Robust baseline with feature importance
- **LightGBM**: Gradient boosting with high performance
- **XGBoost**: Additional gradient boosting for diversity
- **Weighted Ensemble**: Combines predictions (RF: 30%, LGB: 40%, XGB: 30%)

### 4. Validation Strategy
- Time Series Cross-Validation to respect temporal order
- 3-fold splits to evaluate model stability

## Files Structure

```
├── bus_demand_prediction.py    # Main solution script
├── requirements.txt            # Python dependencies
├── README.md                  # This file
├── train_JDXlpm8/train/       # Training data directory
├── test_8gqdJqH.csv           # Test data
├── sample_submission_TQv3O0x.csv  # Sample submission format
└── bus_demand_submission.csv  # Generated predictions (after running)
```

## Usage

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Run the Solution
```bash
python bus_demand_prediction.py
```

### 3. Output
- Generates `bus_demand_submission.csv` with predictions
- Displays model validation scores and prediction statistics

## Model Features

### Core Features (20+)
- `year`, `month`, `day`, `dayofweek`, `dayofyear`
- `week`, `quarter`, `season`
- `is_weekend`, `is_month_start`, `is_month_end`
- `days_to_nearest_holiday`, `is_holiday`, `is_pre_holiday`, `is_post_holiday`
- `srcid`, `destid`

### Route-Specific Features (30+)
- `route_mean`, `route_std`, `route_median`, `route_min`, `route_max`, `route_count`
- `route_dow_0_mean` to `route_dow_6_mean` (day-of-week averages)
- `route_month_1_mean` to `route_month_12_mean` (monthly averages)

## Holiday Calendar

Includes major Indian holidays for 2023-2025:
- Republic Day, Holi, Ram Navami, Good Friday
- Labour Day, Independence Day, Ganesh Chaturthi
- Gandhi Jayanti, Dussehra, Diwali, Christmas

## Model Performance

The ensemble approach typically achieves:
- Cross-validation RMSE: ~400-600 (varies by data split)
- Individual model contributions balanced for stability
- Handles both high-demand and low-demand routes effectively

## Key Insights

1. **Temporal Patterns**: Strong day-of-week and seasonal effects
2. **Route Importance**: Historical route performance is highly predictive
3. **Holiday Impact**: Holidays significantly affect demand patterns
4. **Ensemble Benefits**: Multiple models reduce overfitting risk

## Future Improvements

1. **Advanced Features**: 
   - Weather data integration
   - Economic indicators
   - Regional event calendars

2. **Model Enhancements**:
   - Neural networks for complex patterns
   - Time series specific models (ARIMA, Prophet)
   - Route clustering for similar patterns

3. **Validation**:
   - More sophisticated time series validation
   - Route-stratified validation

## Notes

- Solution handles missing routes by using global statistics
- Predictions are constrained to be non-negative
- Feature engineering is the key differentiator
- Ensemble approach provides robustness across different data patterns
